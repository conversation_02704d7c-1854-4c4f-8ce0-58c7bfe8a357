// MongoDB Script for Test Data Generation
// To run this script, paste it into the MongoDB Compass shell or use the mongo shell.

// --- Clean up existing test data (optional, uncomment to use) ---
// db.organizations.deleteMany({ name: { $in: ["Test Org 1", "Test Org 2"] } });
// db.users.deleteMany({ email: /@test\.com/ });

// --- Generate ObjectIDs ---
const org1Id = new ObjectId();
const org2Id = new ObjectId();

const org1OwnerId = new ObjectId();
const org1AdminId = new ObjectId();
const org1MemberId = new ObjectId();

const org2OwnerId = new ObjectId();
const org2AdminId = new ObjectId();
const org2MemberId = new ObjectId();

// --- Create Organizations ---
const organizationsToInsert = [
  {
    _id: org1Id,
    name: 'Test Org 1',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  {
    _id: org2Id,
    name: 'Test Org 2',
    invites: [],
    cvs: [],
    locations: [],
    planStatus: 'free',
    planTier: 'free',
    muchskillsMembers: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
];

// --- Create Users ---
// Using a pre-hashed password for 'password123' for simplicity from your example.
const hashedPassword =
  '$2b$10$.Nd6d.QGRlUD42ULl1mhKOJT5hCC4kw4bFRY.w/F6Ad3Pv5gysYKy';

const usersToInsert = [
  // Users for Organization 1
  {
    _id: org1OwnerId,
    email: '<EMAIL>',
    firstName: 'Owner',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'OWNER',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  {
    _id: org1AdminId,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'ADMIN',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  {
    _id: org1MemberId,
    email: '<EMAIL>',
    firstName: 'Member',
    lastName: 'Org1',
    password: hashedPassword,
    status: 1,
    organization: org1Id,
    availableOrganizations: [
      {
        orgId: org1Id,
        role: 'MEMBER',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  // Users for Organization 2
  {
    _id: org2OwnerId,
    email: '<EMAIL>',
    firstName: 'Owner',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'OWNER',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  {
    _id: org2AdminId,
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'ADMIN',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
  {
    _id: org2MemberId,
    email: '<EMAIL>',
    firstName: 'Member',
    lastName: 'Org2',
    password: hashedPassword,
    status: 1,
    organization: org2Id,
    availableOrganizations: [
      {
        orgId: org2Id,
        role: 'MEMBER',
        joinedDate: new Date(),
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    __v: 0,
  },
];

// --- Insert Data into Database ---
db.organizations.insertMany(organizationsToInsert);
db.users.insertMany(usersToInsert);

print('✅ Test data created successfully!');
print('Created 2 organizations and 6 users (Owner, Admin, Member for each).');
