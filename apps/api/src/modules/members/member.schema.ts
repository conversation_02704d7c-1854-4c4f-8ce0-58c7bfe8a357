import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { Currency, MemberSource, TimeRange, UserType } from 'shared/types';

import { BaseModel } from '../../db';
import { ICv } from '../../modules/cvs/cv.schema';
import { IOrganization } from '../organization/organization.schema';

export interface ICostRate {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

export interface ISocial {
  name: string;
  link: string;
}

export interface IWorkRecord {
  companyName: string;
  roleTitle?: string;
  description?: string;
  startDate: Date;
  endDate?: Date;
  isCurrent?: boolean;
}

export interface IEducationRecord {
  schoolName: string;
  degree?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface ISkill {
  name: string;
  //Field to divide skills to "tech" and "others"
  type?: string;
  isValidated?: boolean;
  validated?: Date;
  level?: number;
  added?: Date;
  validatedLevel?: number;
}

export interface ICertification {
  name: string;
  issuingOrganization: string;
  notExpiring?: boolean;
  issuedAt?: Date;
  expiredAt?: Date;
  added?: Date;
  license?: string;
  url?: string;
}

//TODO: This interface should be removed, we have a MemberDocument
export interface IMember extends BaseModel {
  avatar?: string;
  firstName: string;
  lastName: string;
  email?: string;
  location?: string;
  telephone?: string;
  currentPosition?: string;
  currentLevel?: string;
  yearsOfExperience?: number;
  languages: string[];
  type?: UserType;
  clients: string[];
  costRate?: ICostRate;
  socials: ISocial[];
  source: MemberSource;
  sourceId?: string;
  cvs: Types.ObjectId[] | ICv[];
  competencesAmount?: number;
  organization: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

export type MemberDocument = Member & Document;

@Schema({ timestamps: true })
export class Member {
  _id: Types.ObjectId;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: true,
  })
  organization: Types.ObjectId | IOrganization;

  @Prop({ type: String })
  avatar?: string;

  @Prop({ type: String, required: true })
  firstName: string;

  @Prop({ type: String })
  lastName?: string;

  @Prop({ type: String })
  email?: string;

  @Prop({ type: String })
  location?: string;

  @Prop({ type: String })
  telephone?: string;

  @Prop({ type: String })
  currentPosition?: string;

  @Prop({ type: String })
  currentLevel?: string;

  @Prop({ type: Number })
  yearsOfExperience?: number;

  @Prop({ type: [String], default: [] })
  languages: string[];

  @Prop({ type: String, enum: UserType })
  type?: UserType;

  @Prop({ type: [String], default: [] })
  clients: string[];

  @Prop({
    type: {
      currency: { type: String, enum: Currency },
      amount: Number,
      timeRange: { type: String, enum: TimeRange },
    },
  })
  costRate?: ICostRate;

  @Prop({
    type: {
      currency: { type: String, enum: Currency },
      amount: Number,
      timeRange: { type: String, enum: TimeRange },
    },
  })
  costToCompany?: ICostRate;

  @Prop({
    type: [
      {
        name: String,
        link: String,
      },
    ],
    default: [],
  })
  socials: ISocial[];

  @Prop({ type: String, enum: MemberSource, required: true })
  source: MemberSource;

  @Prop({ type: String })
  sourceId?: string;

  //FIXME: this field is not needed if member has a ist of competences
  @Prop({ type: Number })
  competencesAmount?: number;

  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'Cv', default: [] })
  cvs: Types.ObjectId[] | ICv[];

  @Prop({
    type: [
      {
        companyName: { type: String, required: true },
        roleTitle: String,
        description: String,
        startDate: { type: Date, required: true },
        endDate: Date,
        isCurrent: Boolean,
      },
    ],
    default: [],
  })
  workExperience: IWorkRecord[];

  @Prop({
    type: [
      {
        schoolName: { type: String, required: true },
        degree: String,
        description: String,
        startDate: Date,
        endDate: Date,
      },
    ],
    default: [],
  })
  education: IEducationRecord[];

  @Prop({
    type: [
      new mongoose.Schema({
        name: { type: String, required: true },
        issuingOrganization: { type: String, required: true },
        notExpiring: Boolean,
        issuedAt: Date,
        expiredAt: Date,
        added: Date,
        license: String,
        url: String,
      }),
    ],
    default: [],
  })
  certifications: ICertification[];

  @Prop({
    type: [
      new mongoose.Schema({
        name: { type: String, required: true },
        isValidated: Boolean,
        validated: Date,
        level: Number,
        added: Date,
        validatedLevel: Number,
        type: String,
      }),
    ],
    default: [],
  })
  skills: ISkill[];
}

export const MemberSchema = SchemaFactory.createForClass(Member);
