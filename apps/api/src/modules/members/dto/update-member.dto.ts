import { Transform, Type } from 'class-transformer';
import {
  IsNumber,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  IsEnum,
  ValidateNested,
  IsBoolean,
  IsDate,
} from 'class-validator';
import { UpdateMemberInput } from 'shared/inputs';
import { UserType, Currency, TimeRange } from 'shared/types';
import { Trim } from 'shared/utils/decorators';

export class CostRateDto {
  @IsEnum(Currency, { message: 'Unknown currency' })
  currency: Currency;

  @IsNumber()
  amount: number;

  @IsEnum(TimeRange, { message: 'Unknown time range' })
  timeRange: TimeRange;
}

export class SocialDto {
  @Trim()
  @IsString()
  name: string;

  @Trim()
  @IsString()
  link: string;
}

export class EducationRecordDtoQ {
  @IsOptional()
  @IsBoolean()
  hidden?: boolean;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'School name cannot be empty. ' })
  schoolName: string;

  @Trim()
  @IsOptional()
  @IsString()
  degree?: string;

  @Trim()
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}

export class WorkHistoryRecordDtoQ {
  @IsOptional()
  @IsBoolean()
  hidden?: boolean;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty. ' })
  companyName: string;

  @Trim()
  @IsOptional()
  @IsString()
  roleTitle?: string;

  @Trim()
  @IsOptional()
  @IsString()
  description?: string;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;
}

export class Certification {
  @Trim()
  @IsString()
  name: string;

  @Trim()
  @IsOptional()
  @IsString()
  issuingOrganization?: string;

  @IsOptional()
  @IsBoolean()
  notExpiring?: boolean;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  issuedAt?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  expiredAt?: Date;

  @Trim()
  @IsOptional()
  @IsString()
  license?: string;

  @Trim()
  @IsOptional()
  @IsString()
  url?: string;
}

export class Skill {
  @Trim()
  @IsString()
  name: string;

  @IsOptional()
  @IsBoolean()
  isValidated?: boolean;

  @IsOptional()
  @IsNumber()
  level?: number;

  @IsOptional()
  @IsString()
  levelString?: string;

  @IsOptional()
  @IsNumber()
  validatedLevel?: number;

  @IsOptional()
  @IsString()
  validatedLevelString?: string;
}

export class UpdateMemberDto implements UpdateMemberInput {
  @IsOptional()
  @IsString()
  avatar?: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'First name cannot be empty. ' })
  firstName: string;

  @Trim()
  @IsOptional()
  @IsString()
  lastName?: string;

  @Transform(({ value }) => (value === '' ? undefined : value))
  @Trim()
  @IsOptional()
  @IsEmail({}, { message: 'Invalid email format. ' })
  email?: string;

  @Trim()
  @IsOptional()
  @IsString()
  location?: string;

  @Trim()
  @IsOptional()
  @IsString()
  telephone?: string;

  @Trim()
  @IsOptional()
  @IsString()
  currentPosition?: string;

  @IsOptional()
  @IsString()
  currentLevel?: string;

  @IsOptional()
  @IsNumber()
  yearsOfExperience?: number;

  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  languages?: string[];

  @IsOptional()
  @IsEnum(UserType, { message: 'Unknown user type' })
  type?: UserType;

  @IsOptional()
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  clients?: string[];

  @IsOptional()
  @ValidateNested()
  @Type(() => CostRateDto)
  costRate?: CostRateDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => CostRateDto)
  costToCompany?: CostRateDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SocialDto)
  socials?: SocialDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkHistoryRecordDtoQ)
  workExperience?: WorkHistoryRecordDtoQ[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EducationRecordDtoQ)
  education?: EducationRecordDtoQ[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Certification)
  certifications?: Certification[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Skill)
  skills?: Skill[];
}
