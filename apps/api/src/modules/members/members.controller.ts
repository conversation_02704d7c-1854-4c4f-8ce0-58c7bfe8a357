import {
  Body,
  Controller,
  Post,
  Get,
  Query,
  UseGuards,
  Param,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { MAX_AVATAR_SIZE } from 'shared/constants';
import { AuthUser } from 'src/modules/global/decorators/user.decorator';
import { AuthUserDto } from 'src/modules/global/dto/auth-user.dto';

import { CreateMemberDto, GetMembersDto } from './dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { MembersService } from './members.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType } from '../stripe/guards/plan-limit.guard';
import { PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@UseGuards(AuthGuard)
@Controller('members')
export class MembersController {
  constructor(private membersService: MembersService) {}

  @UseGuards(PlanLimitGuard)
  @CheckPlanLimit(LimitType.PROFILES)
  @Post('create')
  async createMember(
    @AuthUser() user: AuthUserDto,
    @Body() dto: CreateMemberDto,
  ) {
    return await this.membersService.createMember(dto, user.organization._id);
  }

  @Post('update/:memberId')
  async updateMember(
    @Param('memberId') memberId: string,
    @Body() dto: UpdateMemberDto,
  ) {
    return await this.membersService.updateMember(memberId, dto);
  }

  @UseGuards(AuthGuard)
  @Get()
  async getOrgMembers(
    @AuthUser() user: AuthUserDto,
    @Query() { source, page, itemsPerPage }: GetMembersDto,
  ) {
    const { members, totalMembers } = await this.membersService.getOrgMembers(
      user.organization._id,
      {
        source,
        page,
        itemsPerPage,
      },
    );

    return { members, totalMembers };
  }

  @UseGuards(AuthGuard)
  @Post('avatar-update/:memberId')
  @UseInterceptors(FileInterceptor('avatar'))
  async updateMemberAvatar(
    @Param('memberId') memberId: string,
    @Body() { removeAvatar }: { removeAvatar: string },
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: MAX_AVATAR_SIZE }),
          new FileTypeValidator({ fileType: 'image' }),
        ],
        fileIsRequired: false,
      }),
    )
    avatar?: Express.Multer.File,
  ) {
    if (removeAvatar) {
      return await this.membersService.removeMemberAvatar(memberId);
    }

    return await this.membersService.uploadMemberAvatar(memberId, avatar);
  }
}
