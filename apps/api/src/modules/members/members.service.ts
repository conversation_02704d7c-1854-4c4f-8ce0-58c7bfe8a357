import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import { CreateMemberDto, GetMembersDto } from './dto';
import { Member, MemberDocument } from './member.schema';
import { ICv } from '../cvs/cv.schema';
import { UpdateMemberDto } from './dto/update-member.dto';
import { S3Provider } from '../global/s3.provider';

@Injectable()
export class MembersService {
  constructor(
    @InjectModel(Member.name)
    private memberModel: Model<MemberDocument>,
  ) {}

  async createMember(dto: CreateMemberDto, orgId: string | Types.ObjectId) {
    return await this.memberModel.create({
      ...dto,
      organization: orgId,
    });
  }

  async updateMember(memberId: string | Types.ObjectId, dto: UpdateMemberDto) {
    return await this.memberModel.findByIdAndUpdate(memberId, { $set: dto });
  }

  async getOrgMembers(
    orgId: string | Types.ObjectId,
    { source, page, itemsPerPage }: GetMembersDto,
  ) {
    // Create a filter object with organization ID
    const filter: Record<string, unknown> = { organization: orgId };

    // Add source filter if provided
    if (source) {
      filter.source = source;
    }

    const members = await this.memberModel
      .find(filter)
      .sort({ firstName: 1, lastName: 1, createdAt: 1 })
      .skip((page - 1) * itemsPerPage)
      .limit(itemsPerPage)
      .populate<{ cvs: ICv[] }>('cvs');

    const totalMembers = await this.memberModel.countDocuments(filter);

    return { members, totalMembers };
  }

  async uploadMemberAvatar(memberId: string, avatar?: Express.Multer.File) {
    if (!avatar) {
      throw new Error('File is not passed');
    }

    const sendData = await new S3Provider().save(
      avatar.buffer,
      avatar.mimetype,
      `member/${memberId}/avatar/${avatar.originalname}`,
    );

    //Do we need to store only the file name in db and create the full path to file on client???
    //Idk, so I made it simple
    await this.memberModel.findByIdAndUpdate(memberId, {
      avatar: sendData.Location,
    });
  }

  async removeMemberAvatar(memberId: string) {
    const memberAvatar = await this.memberModel
      .findById(memberId)
      .then((member) => member.avatar);

    if (memberAvatar) {
      const avatarKey = memberAvatar.replace(
        new RegExp('^' + process.env.AWS_BUCKET_URL + '/'),
        '',
      );
      await new S3Provider().delete(avatarKey);

      await this.memberModel.findByIdAndUpdate(memberId, {
        $unset: { avatar: '' },
      });
    }
  }
}
