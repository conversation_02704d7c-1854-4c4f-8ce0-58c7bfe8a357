import { forwardRef, Module } from '@nestjs/common';

import { CaslAbilityFactory } from './casl-ability.factory';
import { UsersModule } from '../users/users.module';
import { PoliciesGuard } from './guards/policies.guard';
import { OrganizationModule } from '../organization/organization.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => OrganizationModule),
  ],
  providers: [CaslAbilityFactory, PoliciesGuard],
  exports: [CaslAbilityFactory],
})
export class CaslModule {}
