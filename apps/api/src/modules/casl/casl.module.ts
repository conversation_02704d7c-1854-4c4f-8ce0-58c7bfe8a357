import { forwardRef, Module } from '@nestjs/common';

import { CaslAbilityFactory } from './casl-ability.factory';
import { AuthPoliciesGuard } from './guards/auth-policies.guard';
import { PoliciesGuard } from './guards/policies.guard';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [forwardRef(() => AuthModule), forwardRef(() => UsersModule)],
  providers: [CaslAbilityFactory, PoliciesGuard, AuthPoliciesGuard],
  exports: [CaslAbilityFactory, PoliciesGuard, AuthPoliciesGuard],
})
export class CaslModule {}
