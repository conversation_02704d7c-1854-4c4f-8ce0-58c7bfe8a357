import {
  AbilityBuilder,
  createMongoAbility,
  ExtractSubjectType,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { UserRole } from 'shared/types';

import { Action, AppAbility, Subjects } from './casl.types';
import { Cv } from '../cvs/cv.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { Member } from '../members/member.schema';
import { Organization } from '../organization/organization.schema';
import { User } from '../users/user.schema';

@Injectable()
export class CaslAbilityFactory {
  createForUser(user: AuthUserDto) {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      createMongoAbility,
    );

    const userRole = user.role;
    const organizationId = user.organization._id;

    // Base permissions for all authenticated users in an organization
    can(Action.Update, User, { _id: user._id });
    // can(Action.Read, User, {
    //   availableOrganizations: { $elemMatch: { orgId: organizationId } },
    // });
    can(Action.Manage, Cv, { organization: organizationId });
    can(Action.Manage, Member, { organization: organizationId });

    // Permissions for administrative roles (Admin, Owner)
    if (userRole === UserRole.ADMIN || userRole === UserRole.OWNER) {
      can(Action.Manage, User, {
        availableOrganizations: { $elemMatch: { orgId: organizationId } },
      });
    }

    // Role-specific permissions
    switch (userRole) {
      case UserRole.OWNER:
        can(Action.Delete, Organization, { _id: organizationId });
        break;

      case UserRole.ADMIN:
        cannot(Action.Delete, Organization).because(
          'Only owners can delete the organization.',
        );
        can(Action.Update, Organization, { _id: organizationId });
        break;
    }

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
