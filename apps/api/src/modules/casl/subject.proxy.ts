import { Injectable, Type } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

import { IPolicySubject } from './casl.interface';
import { Subjects } from './casl.types';
import { OrganizationSubject } from './subjects/organization.subject';
import { UserSubject } from './subjects/user.subject';
import { Organization } from '../organization/organization.schema';
import { User } from '../users/user.schema';

@Injectable()
export class SubjectProxy {
  private subjectMapping = new Map<Subjects, Type<IPolicySubject>>([
    [User, UserSubject],
    [Organization, OrganizationSubject],
  ]);

  constructor(private readonly moduleRef: ModuleRef) {}

  async get(subject: Subjects): Promise<IPolicySubject | undefined> {
    const subjectClass = this.subjectMapping.get(subject);
    if (!subjectClass) {
      return undefined;
    }
    return this.moduleRef.create(subjectClass);
  }
}
