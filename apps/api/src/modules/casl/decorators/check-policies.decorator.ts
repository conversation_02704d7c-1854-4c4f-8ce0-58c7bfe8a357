import { SetMetadata } from '@nestjs/common';

import { Action, AppAbility, Subjects } from '../casl.types';

export interface RequiredPermission {
  action: Action;
  subject: Subjects;
}

export interface IPolicyHandler {
  handle(ability: AppAbility): boolean;
}

export type PolicyHandler =
  | IPolicyHandler
  | ((ability: AppAbility) => boolean)
  | RequiredPermission;

export const CHECK_POLICIES_KEY = 'check_policy';
export const CheckPolicies = (...handlers: PolicyHandler[]) =>
  SetMetadata(CHECK_POLICIES_KEY, handlers);
