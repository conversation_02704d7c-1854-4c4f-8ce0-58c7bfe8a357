import { SetMetadata } from '@nestjs/common';

import { Action, Subjects, AppAbility } from '../casl.types';

export interface RequiredPermission {
  action: Action;
  subject: Subjects;
}

interface IPolicyHandler {
  handle(ability: AppAbility): boolean;
}

type PolicyHandlerCallback = (ability: AppAbility) => boolean;

export type PolicyHandler =
  | IPolicyHandler
  | PolicyHandlerCallback
  | RequiredPermission;

export const CHECK_POLICIES_KEY = 'check_policy';
export const CheckPolicies = (...handlers: PolicyHandler[]) =>
  SetMetadata(CHECK_POLICIES_KEY, handlers);
