import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { UserRole } from 'shared/types';

import { AllowAnonymous } from '../../global/decorators/allow-anonymous.decorator';
import { UsersService } from '../../users/users.service';
import { CaslAbilityFactory } from '../casl-ability.factory';
import { AppAbility } from '../casl.types';
import {
  CHECK_POLICIES_KEY,
  PolicyHandler,
} from '../decorators/check-policies.decorator';

@Injectable()
export class AuthPoliciesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
    private jwtService: JwtService,
    private usersService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route allows anonymous access
    const allowAnonymous = this.reflector.getAllAndOverride<boolean>(
      AllowAnonymous,
      [context.getHandler(), context.getClass()],
    );

    if (allowAnonymous) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    // First, handle authentication
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      const user = await this.usersService.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Attach user to request
      request['user'] = {
        _id: user._id,
        email: user.email,
        role: user.role || UserRole.MEMBER,
        organization:
          user.availableOrganizations?.find(
            (org) => org.orgId.toString() === user.organization?.toString(),
          ) || user.availableOrganizations?.[0],
      };
    } catch {
      throw new UnauthorizedException('Invalid token');
    }

    // Then, handle authorization if policies are defined
    const policyHandlers =
      this.reflector.get<PolicyHandler[]>(
        CHECK_POLICIES_KEY,
        context.getHandler(),
      ) || [];

    // If no policies are defined, allow access (authentication was successful)
    if (policyHandlers.length === 0) {
      return true;
    }

    const { user } = request;
    const ability = this.caslAbilityFactory.createForUser(user);

    return policyHandlers.every((handler) =>
      this.execPolicyHandler(handler, ability),
    );
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  private execPolicyHandler(handler: PolicyHandler, ability: AppAbility) {
    if (typeof handler === 'function') {
      return handler(ability);
    }

    // Handle RequiredPermission pattern (legacy)
    if ('action' in handler && 'subject' in handler) {
      return ability.can(handler.action, handler.subject);
    }

    // Handle IPolicyHandler pattern
    return handler.handle(ability);
  }
}
