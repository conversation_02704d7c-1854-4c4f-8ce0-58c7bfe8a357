import { ForbiddenError } from '@casl/ability';
import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Organization } from 'src/modules/organization/organization.schema';
import { OrganizationService } from 'src/modules/organization/organization.service';
import { User } from 'src/modules/users/user.schema';
import { UsersService } from 'src/modules/users/users.service';

import { CaslAbilityFactory } from '../casl-ability.factory';
import {
  CHECK_POLICIES_KEY,
  RequiredPermission,
} from '../decorators/check-policies.decorator';

@Injectable()
export class PoliciesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
    private usersService: UsersService,
    private organizationService: OrganizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers =
      this.reflector.get<RequiredPermission[]>(
        CHECK_POLICIES_KEY,
        context.getHandler(),
      ) || [];

    const request = context.switchToHttp().getRequest();
    const { user, params } = request;
    const ability = this.caslAbilityFactory.createForUser(user);

    try {
      for (const handler of policyHandlers) {
        let subject;
        if (handler.subject === User) {
          const subjectId = params.userId || params.id;
          if (!subjectId) {
            // If no ID is provided, we check the ability against the class.
            subject = handler.subject;
          } else {
            subject = await this.usersService.findById(subjectId);
            if (!subject) {
              throw new NotFoundException('User not found');
            }
          }
        } else if (handler.subject === Organization) {
          subject = await this.organizationService.findById(
            user.organization._id,
          );
          if (!subject) {
            throw new NotFoundException('Organization not found');
          }
        } else {
          subject = handler.subject;
        }
        ForbiddenError.from(ability).throwUnlessCan(handler.action, subject);
      }
      return true;
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }
  }
}
