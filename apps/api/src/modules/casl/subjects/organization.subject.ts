import {
  ExecutionContext,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Organization } from 'src/modules/organization/organization.schema';
import { OrganizationService } from 'src/modules/organization/organization.service';

import { IPolicySubject } from '../casl.interface';

@Injectable()
export class OrganizationSubject implements IPolicySubject {
  constructor(private readonly organizationService: OrganizationService) {}

  async load(ctx: ExecutionContext): Promise<Organization | undefined> {
    const request = ctx.switchToHttp().getRequest();
    const organizationId = request.user.organization._id;

    if (!organizationId) {
      return undefined;
    }

    const organization =
      await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }
}
