import {
  ExecutionContext,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { User } from 'src/modules/users/user.schema';
import { UsersService } from 'src/modules/users/users.service';

import { IPolicySubject } from '../casl.interface';

@Injectable()
export class UserSubject implements IPolicySubject {
  constructor(private readonly usersService: UsersService) {}

  async load(ctx: ExecutionContext): Promise<User | undefined> {
    const request = ctx.switchToHttp().getRequest();
    const userId = request.params.userId || request.params.id;

    if (!userId) {
      return undefined;
    }

    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }
}
