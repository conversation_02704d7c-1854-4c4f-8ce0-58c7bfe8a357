import {
  Controller,
  Param,
  Body,
  UseGuards,
  Patch,
  Post,
  HttpCode,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UpdateUserDto } from 'shared/dto/user/update-user.dto';

import { User } from './user.schema';
import { UsersService } from './users.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import {
  CheckPolicies,
  RequiredPermission,
} from '../casl/decorators/check-policies.decorator';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @UseGuards(AuthGuard)
  @CheckPolicies({ action: Action.Update, subject: User })
  @Patch(':userId')
  @HttpCode(HttpStatus.OK)
  async updateUser(
    @Param('userId') userId: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<User | null> {
    return this.usersService.updateUser(userId, updateUserDto);
  }

  @UseGuards(AuthGuard)
  @CheckPolicies({ action: Action.Update, subject: User })
  @Post(':userId/avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  @HttpCode(HttpStatus.OK)
  async uploadAvatar(
    @Param('userId') userId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: '.(png|jpeg|jpg|gif|webp)' }),
        ],
      }),
    )
    avatarFile: Express.Multer.File,
  ): Promise<User | null> {
    return this.usersService.uploadUserAvatar(userId, avatarFile);
  }
}
