import { HttpService } from '@nestjs/axios';
import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosError } from 'axios';
import { escapeRegExp } from 'lodash';
import { Model } from 'mongoose';
import { catchError, firstValueFrom } from 'rxjs';
import { CvProfileFilter, MemberSource, MuchskillsMember } from 'shared/types';

import { CVProfile } from './muchskills.schema';
import { ISkill, Member, MemberDocument } from '../members/member.schema';
import { MembersService } from '../members/members.service';
import {
  Organization,
  OrganizationDocument,
} from '../organization/organization.schema';
import { OrganizationService } from '../organization/organization.service';

@Injectable()
export class MuchskillsService {
  private readonly logger = new Logger(MuchskillsService.name);
  constructor(
    //Models
    @InjectModel(Member.name)
    private memberModel: Model<MemberDocument>,
    @InjectModel(Organization.name)
    private organizationModel: Model<OrganizationDocument>,
    //Services
    private readonly httpService: HttpService,
    private readonly membersService: MembersService,
    private readonly organizationService: OrganizationService,
  ) {}

  async syncProfiles(orgId: string) {
    const authToken =
      await this.organizationService.getMuchskillsAuthToken(orgId);

    if (!authToken) {
      throw new UnauthorizedException(
        'MuchSkills auth token not found. Please connect with MuchSkills first.',
      );
    }

    const { data } = await firstValueFrom(
      this.httpService
        .post<CVProfile[]>(
          'https://test.muchskills.com/api/v1/profile/search',
          {},
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
            },
          },
        )
        .pipe(
          catchError(async (error: AxiosError) => {
            if (error.response.status === 401) {
              await this.organizationModel.findOneAndUpdate(
                { _id: orgId },
                { $set: { 'muchskillsIntegration.connected': false } },
              );
            }
            this.logger.error(error.response.data);
            throw 'An error happened!';
          }),
        ),
    );

    const preparedMsMembersData = data.map((member) => {
      const skillsMap = member.skillTypes
        .sort(
          (a, b) =>
            Number(a.type === 'Technical skills') -
            Number(b.type === 'Technical skills'),
        )
        .reduce((res, st) => {
          st.skills.forEach((skill) => {
            return res.set(`${skill.name}${skill.level}`, {
              ...skill,
              added: skill.added && new Date(skill.added),
              validated: skill.validated && new Date(skill.validated),
              type: st.type === 'Technical skills' ? 'tech' : undefined,
            });
          });

          return res;
        }, new Map<string, ISkill>());

      return {
        profile: {
          ...member.profile,
          workExperience: member.profile.experience.map((workRec) => ({
            companyName: workRec.name,
            roleTitle: workRec.role,
            description: workRec.description,
            startDate: workRec.startTime,
            endDate: workRec.endTime,
            isCurrent: !workRec.endTime,
          })),
          education: member.profile.education.map((educationRec) => ({
            schoolName: educationRec.name,
            degree: educationRec.degree,
            description: educationRec.description,
            startDate: educationRec.startTime,
            endDate: educationRec.endTime,
          })),
        },
        certifications: member.certifications,
        skills: Array.from(skillsMap.values()),
      };
    });

    //Set ms members to org
    await this.organizationModel.findOneAndUpdate(
      { _id: orgId },
      { $set: { muchskillsMembers: preparedMsMembersData } },
    );

    //Update existed members
    const existedMembers = await this.memberModel.find({
      organization: orgId,
      email: { $in: preparedMsMembersData.map((m) => m.profile.email) },
    });
    //TODO: put this update into the queue
    const updates = existedMembers.map((existedMember) => {
      const msMemberData = preparedMsMembersData.find(
        (msMem) => msMem.profile.email === existedMember.email,
      );
      const [firstName, ...lastNameParts] =
        msMemberData.profile.name.split(' ');
      const lastName = lastNameParts.join(' ');

      return {
        updateOne: {
          filter: { organization: orgId, email: existedMember.email },
          update: {
            $set: {
              avatar: msMemberData.profile.image,
              firstName,
              lastName: lastName,
              email: msMemberData.profile.email,
              location: msMemberData.profile.location,
              currentPosition: msMemberData.profile.title,
              socials: msMemberData.profile.links?.map((link) => ({
                name: 'placeholder',
                link,
              })),
              source: MemberSource.muchskills,
              workExperience: msMemberData.profile.workExperience,
              education: msMemberData.profile.education,
              certifications: msMemberData.certifications,
              skills: msMemberData.skills,
            },
          },
        },
      };
    });

    await this.memberModel.bulkWrite(updates);
    await this.organizationModel.findOneAndUpdate(
      { _id: orgId },
      { $set: { 'muchskillsIntegration.lastSync': new Date() } },
    );
  }

  async getProfiles(
    orgId: string,
    page: number,
    itemsPerPage: number,
    searchValue: string,
    filter: CvProfileFilter,
  ) {
    const orgMembers = await this.memberModel.find({ organization: orgId });
    const filteredMuchskillsMembers = await this.organizationModel
      .findOne({ _id: orgId })
      .then((org) => org.muchskillsMembers)
      .then((members) => {
        if (searchValue) {
          const regex = new RegExp(escapeRegExp(searchValue), 'i');
          return members.filter((member) => regex.test(member.profile.name));
        }

        return members;
      })
      .then((members) => {
        if (filter === CvProfileFilter.all) return members;

        const orgMembersEmails = new Set(orgMembers.map((mem) => mem.email));
        if (filter === CvProfileFilter.hasCvProf)
          return members.filter((m) => orgMembersEmails.has(m.profile.email));
        if (filter === CvProfileFilter.hasNoCvProf)
          return members.filter((m) => !orgMembersEmails.has(m.profile.email));
      });

    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    const membersToReturn = filteredMuchskillsMembers
      .sort((a, b) => a.profile.name.localeCompare(b.profile.name))
      .slice(startIndex, endIndex)
      .reduce((res: MuchskillsMember[], member) => {
        const orgMember = orgMembers.find(
          (orgMem) => orgMem.email === member.profile.email,
        );

        res.push({
          email: member.profile.email,
          name: member.profile.name,
          image: member.profile.image,
          title: member.profile.title,
          location: member.profile.location,
          competencesCount: member.skills.length + member.certifications.length,
          cvsCount: orgMember?.cvs.length || 0,
          profileExist: !!orgMember,
          localId: orgMember?._id,
        });

        return res;
      }, []);

    return {
      members: membersToReturn,
      total: filteredMuchskillsMembers.length,
    };
  }

  async createMemberFromMs(orgId: string, email: string) {
    const msMemberData = await this.organizationModel
      .findOne({ _id: orgId })
      .then((org) =>
        org.muchskillsMembers.find((member) => member.profile.email === email),
      );

    if (!msMemberData) {
      throw new Error('Member is not found');
    }

    const [firstName, ...lastNameParts] = msMemberData.profile.name.split(' ');
    const lastName = lastNameParts.join(' ');

    await this.membersService.createMember(
      {
        avatar: msMemberData.profile.image,
        firstName,
        lastName: lastName,
        email: msMemberData.profile.email,
        location: msMemberData.profile.location,
        currentPosition: msMemberData.profile.title,
        socials: msMemberData.profile.links.map((link) => ({
          name: 'placeholder',
          link,
        })),
        source: MemberSource.muchskills,
        workExperience: msMemberData.profile.workExperience,
        education: msMemberData.profile.education,
        certifications: msMemberData.certifications,
        skills: msMemberData.skills,
      },
      orgId,
    );
  }
}
