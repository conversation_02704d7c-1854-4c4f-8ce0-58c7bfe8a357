import { Controller, Delete, Get, Post, Param, Query } from '@nestjs/common';

import { Organization } from './organization.schema';
import { OrganizationService } from './organization.service';
import { Action } from '../casl/casl.types';
import { CheckPolicies } from '../casl/decorators/check-policies.decorator';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  async getOrganization(@AuthUser() user: AuthUserDto) {
    const { _id: orgId } = user.organization;

    return await this.organizationService.findById(orgId);
  }

  @Get('full-info')
  getOrganizationFullInfo(@AuthUser() user: AuthUserDto) {
    return this.organizationService.getOrganizationFullInfo(
      user.organization._id,
    );
  }

  @Get('users')
  getOrganizationUsers(
    @AuthUser() user: AuthUserDto,
    @Query('search') searchTerm?: string,
  ) {
    return this.organizationService.getOrganizationUsers(
      user.organization._id,
      searchTerm,
    );
  }

  @Delete()
  @CheckPolicies({ action: Action.Delete, subject: Organization })
  deleteOrganization(@AuthUser() user: AuthUserDto) {
    return this.organizationService.deleteOrganization(user.organization._id);
  }

  @Post('set-active/:organizationId')
  setActiveOrganization(
    @AuthUser() user: AuthUserDto,
    @Param('organizationId') organizationId: string,
  ) {
    return this.organizationService.setActiveOrganization(
      user._id,
      organizationId,
    );
  }

  @Get('all')
  getAllOrganizations(@AuthUser() user: AuthUserDto) {
    return this.organizationService.getUserOrganizations(user._id);
  }
}
