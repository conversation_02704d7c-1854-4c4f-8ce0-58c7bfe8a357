import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { OrganizationController } from './organization.controller';
import { Organization, OrganizationSchema } from './organization.schema';
import { OrganizationService } from './organization.service';
import { Cv, CvSchema } from '../cvs/cv.schema';
import { CvsModule } from '../cvs/cvs.module';
import { InviteModule } from '../invite/invite.module';
import { Member, MemberSchema } from '../members/member.schema';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    forwardRef(() => InviteModule),
    forwardRef(() => UsersModule),
    forwardRef(() => CvsModule),
    MongooseModule.forFeature([
      { name: Organization.name, schema: OrganizationSchema },
      { name: Member.name, schema: MemberSchema },
      { name: Cv.name, schema: CvSchema },
    ]),
  ],
  providers: [OrganizationService],
  controllers: [OrganizationController],
  exports: [OrganizationService],
})
export class OrganizationModule {}
