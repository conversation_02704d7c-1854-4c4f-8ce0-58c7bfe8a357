import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import axios from 'axios';
import { Model, Types } from 'mongoose';
import { CvStatus, Template } from 'shared/types';

import {
  Cv,
  CvDocument,
  CvSections,
  EducationDataItem,
  WorkHistoryDataItem,
} from './cv.schema';
import { CvPreferencesDto, UpdateCvDto } from './dto';
import { Member, MemberDocument } from '../members/member.schema';
import { UpdateCvSectionsDto } from './dto/update-cv-sections.dto';

@Injectable()
export class CvsService {
  constructor(
    @InjectModel(Cv.name) private cvModel: Model<CvDocument>,
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
  ) {}

  async regenerateCv(cvId: string, query: string) {
    console.log(cvId, query);
    return 1;
  }

  async getMemberCvs(memberId: string) {
    return await this.cvModel.find({
      member: memberId,
    });
  }

  async createCv(
    memberId: string,
    organizationId: Types.ObjectId,
    dto: CvPreferencesDto,
  ) {
    try {
      const member = await this.memberModel.findById(memberId);

      const sections: CvSections = {
        personalInfo: {
          order: 1,
          title: 'Personal Information',
          active: true,
          data: {
            firstName: { value: member.firstName, active: true },
            lastName: { value: member.lastName, active: true },
            jobTitle: { value: member.currentPosition, active: true },
            location: { value: member.location, active: true },
            nationality: { value: '', active: true },
            email: { value: member.email, active: true },
            telephone: { value: member.telephone, active: true },
            socials: { inputs: [], active: true },
          },
        },
        aboutMe: {
          order: 2,
          title: 'About Me',
          active: true,
          data: {
            description: '',
          },
        },
        workHistory: {
          order: 3,
          title: 'Work History',
          active: true,
          data: member.workExperience.map(
            (exp): WorkHistoryDataItem => ({
              active: true,
              companyName: exp.companyName,
              roleTitle: exp.roleTitle,
              description: exp.description,
              startDate: exp.startDate,
              endDate: exp.endDate,
              isCurrent: exp.isCurrent,
            }),
          ),
        },
        education: {
          order: 4,
          title: 'Education',
          active: true,
          data: member.education.map(
            (ed): EducationDataItem => ({
              active: true,
              schoolName: ed.schoolName,
              degree: ed.degree,
              description: ed.description,
              startDate: ed.startDate,
              endDate: ed.endDate,
            }),
          ),
        },
        certifications: {
          order: 5,
          title: 'Certifications',
          active: true,
          data: {
            description: '',
          },
        },
        skills: {
          order: 6,
          title: 'Skills',
          active: true,
          data: {
            description: '',
          },
        },
        languages: {
          order: 7,
          title: 'Languages',
          active: true,
          data: {
            languages: member.languages,
          },
        },
      };

      const newCv = await this.cvModel.create({
        member: memberId,
        organization: organizationId,
        status: CvStatus.draft,
        template: Template.europass,
        preferences: dto,
        sections,
      });

      await this.memberModel.updateOne(
        { _id: memberId },
        {
          $push: { cvs: newCv.id },
        },
      );

      return newCv;
    } catch (e) {
      if (e.code === 11000) {
        throw new BadRequestException('Member already has CV with this alias.');
      }

      throw new Error(e.message);
    }
  }

  async updateCv(cvId: string | Types.ObjectId, dto: UpdateCvDto) {
    try {
      return await this.cvModel.findByIdAndUpdate(cvId, {
        $set: { ...dto },
      });
    } catch (e) {
      if (e.code === 11000) {
        throw new BadRequestException('Member already has CV with this alias.');
      }
    }
  }

  async updateCvSections(
    cvId: string | Types.ObjectId,
    dto: UpdateCvSectionsDto,
  ) {
    return await this.cvModel.findByIdAndUpdate(cvId, {
      $set: { sections: dto },
    });
  }

  async duplicateCv(cvId: string | Types.ObjectId) {
    const originalCv = await this.cvModel.findById(cvId);

    let counter = 1;
    while (
      await this.cvModel.exists({
        member: originalCv.member,
        'preferences.title': `${originalCv.preferences.title} duplicate #${counter}`,
      })
    ) {
      counter++;
    }

    const duplicatedData = originalCv.toObject();
    delete duplicatedData._id;

    return await this.cvModel.create({
      ...duplicatedData,
      'preferences.title': `${originalCv.preferences.title} duplicate #${counter}`,
    });
  }

  async deleteCv(cvId: string | Types.ObjectId) {
    return await this.cvModel.findByIdAndDelete(cvId);
  }

  async deleteOrganizationCvs(organizationId: string | Types.ObjectId) {
    await this.cvModel.deleteMany({ organization: organizationId });
  }

  async getMemberBase64Avatar(memberId: string | Types.ObjectId) {
    try {
      const avatarUrl = await this.memberModel
        .findById(memberId)
        .then((member) => member?.avatar);

      if (!avatarUrl) {
        return ' ';
      }

      const response = await axios.get(avatarUrl, {
        responseType: 'arraybuffer',
      });
      const contentType = response.headers['content-type'] || 'image/png';
      const base64 = Buffer.from(response.data, 'binary').toString('base64');
      return `data:${contentType};base64,${base64}`;
    } catch (err) {
      throw new Error('Something went wrong!');
    }
  }
}
