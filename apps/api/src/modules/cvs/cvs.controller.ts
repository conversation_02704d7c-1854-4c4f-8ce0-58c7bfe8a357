import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { RegenerateCvDto } from 'shared/dto';

import { CvsService } from './cvs.service';
import { CvPreferencesDto, UpdateCvDto } from './dto';
import { UpdateCvSectionsDto } from './dto/update-cv-sections.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType } from '../stripe/guards/plan-limit.guard';
import { PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@UseGuards(AuthGuard)
@Controller('cvs')
export class CvsController {
  constructor(private cvsService: CvsService) {}

  @UseGuards(PlanLimitGuard)
  @CheckPlanLimit(LimitType.CVS) // Apply decorator
  @Post('create/:memberId')
  async createCv(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
    @Body() dto: CvPreferencesDto,
  ) {
    const newCv = await this.cvsService.createCv(
      memberId,
      user.organization._id,
      dto,
    );

    return newCv;
  }

  @Post('update/:cvId')
  async updateCv(@Param('cvId') cvId: string, @Body() dto: UpdateCvDto) {
    return await this.cvsService.updateCv(cvId, dto);
  }

  @Post('update-sections/:cvId')
  async updateSectionsCv(
    @Param('cvId') cvId: string,
    @Body() dto: UpdateCvSectionsDto,
  ) {
    return await this.cvsService.updateCvSections(cvId, dto);
  }

  @Post('duplicate/:cvId')
  async duplicateCv(@Param('cvId') cvId: string) {
    return await this.cvsService.duplicateCv(cvId);
  }

  @Delete('delete/:cvId')
  async deleteCv(@Param('cvId') cvId: string) {
    return await this.cvsService.deleteCv(cvId);
  }

  @Get('byMember/:memberId')
  getMemberCvs(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return this.cvsService.getMemberCvs(memberId);
  }

  @Post('regenerate/:id')
  regenerateCv(@Param('id') id: string, @Body() dto: RegenerateCvDto) {
    return this.cvsService.regenerateCv(id, dto.query);
  }

  @Get('getMemberBase64Avatar/:memberId')
  getMemberBase64Avatar(@Param('memberId') memberId: string) {
    return this.cvsService.getMemberBase64Avatar(memberId);
  }
}
