import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerModule } from '@nestjs/throttler';
import { LoggerModule } from 'nestjs-pino';
import { APP_GUARD } from '@nestjs/core';

import { AppController } from './app.controller';
import { AuthModule } from './modules/auth/auth.module';
import { CaslModule } from './modules/casl/casl.module';
import { AuthPoliciesGuard } from './modules/casl/guards/auth-policies.guard';
import { CvsModule } from './modules/cvs/cvs.module';
import { GlobalModule } from './modules/global/global.module';
import { InviteModule } from './modules/invite/invite.module';
import { MembersController } from './modules/members/members.controller';
import { MembersModule } from './modules/members/members.module';
import { MuchskillsModule } from './modules/muchskills/muchskills.module';
import { OrganizationModule } from './modules/organization/organization.module';
import { StripeModule } from './modules/stripe/stripe.module'; // Import StripeModule
import { TokenModule } from './modules/token/token.module';
import { UsersModule } from './modules/users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 100,
      },
    ]),
    LoggerModule.forRoot({
      pinoHttp: {
        level: 'info',
        autoLogging:
          process.env.NODE_ENV === 'development'
            ? process.env.DISABLE_REQUEST_LOGS !== 'true'
            : process.env.DISABLE_REQUEST_LOGS === 'false',
        ...(process.env.NODE_ENV === 'development' && {
          transport: {
            target: 'pino-pretty',
            options: {
              colorize: true,
              translateTime: 'SYS:standard',
              ignore: 'pid,hostname',
            },
          },
        }),
      },
    }),
    MongooseModule.forRoot(process.env.MONGO_URI),
    OrganizationModule,
    MembersModule,
    GlobalModule,
    CvsModule,
    AuthModule,
    UsersModule,
    TokenModule,
    InviteModule,
    MuchskillsModule,
    StripeModule, // Add StripeModule here
    CaslModule,
  ],
  controllers: [AppController, MembersController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthPoliciesGuard,
    },
  ],
})
export class AppModule {}
