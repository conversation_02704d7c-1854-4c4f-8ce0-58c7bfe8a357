import { AxiosError } from 'axios';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import {
  CvPreferencesInput,
  CreateMemberInput,
  UpdateCvInput,
  UpdateMemberInput,
  UserInput,
  UpdateCvSectionsInput,
} from 'shared/inputs';
import {
  Paging,
  MemberSource,
  CvProfileFilter,
  PaginatedMuchskillsMembers,
  Cv,
  PaginatedMembers,
  UserRole,
} from 'shared/types';

import api from '../api';

export const loginRequest = async (dto: SignInDto, token?: string) => {
  const response = await api.post('/login', dto, {
    params: token ? { token } : undefined,
  });

  if (response.data.organization) {
    localStorage.setItem(
      'organization',
      JSON.stringify({
        id: response.data.organization.id,
        name: response.data.organization.name,
        photo: response.data.organization.photo,
      }),
    );
  }

  return response.data;
};

export const getMembersRequest = async (paging: Paging) => {
  const response = await api.get('/members', {
    params: {
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
    },
  });

  return response.data as PaginatedMembers;
};

export const getMembersBySourceRequest = async (
  importType: MemberSource,
  paging: Paging,
) => {
  const response = await api.get('/members', {
    params: {
      source: importType,
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
    },
  });
  return response.data;
};

export const getMuchskillsMembers = async ({
  orgId,
  paging,
  searchValue,
  filter,
}: {
  orgId: string;
  paging: Paging;
  searchValue: string;
  filter: CvProfileFilter;
}) => {
  const response = await api.post(`/muchskills/members/${orgId}`, {
    page: paging.page,
    itemsPerPage: paging.itemsPerPage,
    searchValue,
    filter,
  });
  return response.data as PaginatedMuchskillsMembers;
};

export const syncMuchskillsMembers = async (orgId: string) => {
  await api.post(`/muchskills/members-sync/${orgId}`);
};

export const createMemberFromMuchskills = async ({
  orgId,
  email,
}: {
  orgId: string;
  email: string;
}) => {
  await api.post(`/muchskills/create-member/${orgId}/${email}`);
};

export const createMemberRequest = async (
  dto: CreateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post('/members/create', dto);
    const memberId = response.data._id;

    if (avatar && memberId) {
      const formData = new FormData();
      formData.append('avatar', avatar);

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateMemberRequest = async (
  memberId: string,
  dto: UpdateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post(`/members/update/${memberId}`, dto);

    if (avatar !== undefined) {
      const formData = new FormData();

      if (avatar) {
        formData.append('avatar', avatar);
      } else {
        formData.append('removeAvatar', 'true');
      }

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const regenerateCvRequest = async (id: string, aiQuery: string) => {
  const response = await api.post(`/cvs/regenerate/${id}`, {
    query: aiQuery,
  });
  return response.data;
};

export const createCvRequest = async (
  memberId: string,
  dto: CvPreferencesInput,
) => {
  try {
    const response = await api.post(`/cvs/create/${memberId}`, dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateCvRequest = async (cvId: string, dto: UpdateCvInput) => {
  try {
    const response = await api.post(`/cvs/update/${cvId}`, dto);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateCvSectionsRequest = async (
  cvId: string,
  sections: UpdateCvSectionsInput,
) => {
  try {
    const response = await api.post(`/cvs/update-sections/${cvId}`, sections);
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const duplicateCvRequest = async (cvId: string) => {
  const response = await api.post(`/cvs/duplicate/${cvId}`);
  return response.data;
};

export const deleteCvRequest = async (cvId: string) => {
  const response = await api.delete(`/cvs/delete/${cvId}`);
  return response.data;
};

export const getCvsByMemberRequest = async (memberId: string) => {
  const response = await api.get(`/cvs/byMember/${memberId}`);
  return response.data as Cv[];
};

export const getMemberBase64Avatar = async (memberId: string) => {
  const response = await api.get(`/cvs/getMemberBase64Avatar/${memberId}`);
  return response.data as string;
};

export const signUpRequest = async (dto: SignUpDto, token?: string) => {
  const response = await api.post('/sign-up', dto, {
    params: token ? { token } : undefined,
  });
  return response.data;
};

export const requestPasswordResetEmail = async (email: string) => {
  const response = await api.post('/reset-password-request', { email });
  return response.data;
};

export const resetPasswordRequest = async (dto: ResetPasswordDto) => {
  const response = await api.post('/reset-password', dto);
  return response.data;
};

export const logoutRequest = async () => {
  const response = await api.post('/sign-out');
  localStorage.removeItem('organization');
  return response.data;
};

export const connectToMuchskills = async (orgId: string, token: string) => {
  try {
    const response = await api.post(
      `/muchskills/connect/${orgId}`,
      {},
      {
        headers: {
          'ms-token': token,
        },
      },
    );

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const disconnectMuchskills = async (orgId: string) => {
  const response = await api.post(`/muchskills/disconnect/${orgId}`);
  return response.data;
};

export const getOrganizationUsersRequest = async (searchTerm?: string) => {
  const response = await api.get('/organization/users', {
    params: searchTerm ? { search: searchTerm } : undefined,
  });
  return response.data;
};

/*
export const getOrganizationInvitesRequest = async () => {
  const response = await api.get('/invites');
  return response.data;
};
*/

export const createOrganizationInviteRequest = async (dto: {
  email: string;
  role: UserRole;
}) => {
  const response = await api.post('/invites', dto);
  return response.data;
};

export const resendOrganizationInviteRequest = async (inviteId: string) => {
  const response = await api.post(`/invites/${inviteId}/resend`);
  return response.data;
};

export const deleteOrganizationRequest = async () => {
  const response = await api.delete('/organization');
  return response.data;
};

export const updateUserRequest = async (
  userId: string,
  dto: Partial<UserInput>,
) => {
  // Separate avatar if it's a File
  const userData = { ...dto };
  let avatarFile: File | null = null;

  if (dto.avatar && dto.avatar instanceof File) {
    avatarFile = dto.avatar;
    delete userData.avatar; // Don't send File object in JSON payload
  }

  const response = await api.patch(`/users/${userId}`, userData);

  // If there's an avatar file, upload it separately
  // This assumes a backend endpoint like /users/{userId}/avatar exists
  // and can handle FormData. You might need to adjust this based on your API.
  if (avatarFile) {
    const formData = new FormData();
    formData.append('avatar', avatarFile);
    // TODO: Confirm or create this endpoint on the backend
    await api.post(`/users/${userId}/avatar`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  }

  return response.data;
};

export const getAllOrganizationsRequest = async () => {
  const response = await api.get('/organization/all');
  return response.data;
};

export const setActiveOrganizationRequest = async (organizationId: string) => {
  const response = await api.post(`/organization/set-active/${organizationId}`);
  return response.data;
};

export const getOrganizationRequest = async () => {
  const response = await api.get('/organization');
  return response.data;
};

export const acceptInviteRequest = async (token: string) => {
  const response = await api.post('/invites/accept', { token });
  return response.data;
};

export const getInviteDetailsRequest = async (token: string) => {
  const response = await api.get(`/invites/details/${token}`);
  return response.data;
};

export const createStripePortalSessionRequest = async (
  dto?: Record<string, never>,
) => {
  const response = await api.post('/stripe/create-portal-session', dto || {});
  return response.data as { url: string };
};

export const getStripeProductsRequest = async () => {
  const response = await api.get('/stripe/products');
  return response.data;
};

export const getOrganizationFullInfoRequest = async () => {
  const response = await api.get('/organization/full-info');
  return response.data;
};
