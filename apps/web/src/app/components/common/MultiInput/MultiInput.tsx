import { X } from 'lucide-react';

import { ButtonSecondary, Input } from '../../common';

import { parseHostName } from '@/helpers/urlUtils';

const SOCIAL_ICONS: { [key: string]: string } = {
  facebook: 'facebook',
  instagram: 'instagram',
  linkedin: 'linkedin',
  youtube: 'youtube',
  tiktok: 'tiktok',
  github: 'github',
  pinterest: 'pinterest',
  dribbble: 'dribbble',
  twitch: 'twitch',
  reddit: 'reddit',
  behance: 'behance',
  medium: 'medium',
};

function getSocialIcon(url: string) {
  const hostname = parseHostName(url);

  const match = Object.keys(SOCIAL_ICONS).find((key) =>
    hostname?.includes(key),
  );
  const icon = match ? SOCIAL_ICONS[match] : 'globe';
  return (
    <img
      src={`/socialLogos/${icon}.svg`}
      alt={match ?? 'globe'}
      className="w-5 h-5"
    />
  );
}

interface MultiInputProps {
  links: string[];
  onChange: (data: string[]) => void;
}

export function MultiInput({ links, onChange }: MultiInputProps) {
  const handleChange = (index: number, value: string) => {
    const updated = [...links];
    updated[index] = value;
    onChange(updated);
  };

  const handleAdd = () => {
    onChange([...links, '']);
  };

  const handleRemove = (index: number) => {
    onChange(links.filter((_, i) => i !== index));
  };

  return (
    <div className="flex flex-col gap-4">
      {(!links.length ? [''] : links).map((link, index) => (
        <div key={index} className="flex items-center gap-2">
          <div className="w-6 h-6 flex items-center justify-center">
            {getSocialIcon(link)}
          </div>
          <Input
            wrapperClassName="flex-grow"
            type="url"
            value={link}
            placeholder="https://linkedin.com/you"
            onChange={(e) => handleChange(index, e.target.value)}
          />
          <div onClick={() => handleRemove(index)}>
            <X className="w-4 h-4" />
          </div>
        </div>
      ))}
      <ButtonSecondary className="ml-auto" type="button" onClick={handleAdd}>
        Add new
      </ButtonSecondary>
    </div>
  );
}
