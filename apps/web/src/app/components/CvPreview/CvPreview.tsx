import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { useMemo, useState } from 'react';
import {
  CvStatus,
  cvStatusData,
  Section,
  Template,
  templateData,
} from 'shared/types';

import { EuropassTemplate } from '../../../assets/templates';
// import { AiChat } from '../../components';
import {
  ButtonSecondary,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../common';

import { getMemberBase64Avatar, updateCvRequest } from '@/helpers/requests';

interface CvPreviewProps {
  cvId: string;
  memberId: string;
  sections: Section[];
  onEdit: () => void;
}

export function CvPreview({
  cvId,
  memberId,
  sections,
  onEdit,
}: CvPreviewProps) {
  const queryClient = useQueryClient();

  const [cvStatus, setCvStatus] = useState<CvStatus>(CvStatus.draft);
  const [template, setTemplate] = useState<Template>(Template.europass);

  const { data: base64Avatar } = useQuery({
    queryKey: ['base64Avatar', { memberId }],
    queryFn: () => getMemberBase64Avatar(memberId),
  });

  const { mutate: updateStatus } = useMutation({
    mutationFn: (status: CvStatus) => updateCvRequest(cvId, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId }],
      });
    },
  });

  const cvStatusDropdown = (
    <div className="flex items-center space-x-1">
      <span className="text-smalldoge-4">Status:</span>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center cursor-pointer px-2 py-1 rounded-[2px] bg-msYellow-1">
            <span className="text-smalldoge-4">
              {cvStatusData[cvStatus].name}
            </span>
            <ChevronDown className="flex-shrink-0" size={16} />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          collisionPadding={10}
          align="end"
          className="w-38 prevent-drawer-outside-click"
        >
          <DropdownMenuGroup>
            {Object.values(CvStatus).map((status) => (
              <DropdownMenuItem
                key={status}
                onClick={() => {
                  setCvStatus(status);
                  updateStatus(status);
                }}
              >
                <span className="text-smalldoge-3">
                  {cvStatusData[status].name}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  const templateDropdown = (
    <div className="flex items-center space-x-1">
      <span className="text-smalldoge-4">Template:</span>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center cursor-pointer">
            <span className="text-smalldoge-4 font-bold">
              {templateData[template]}
            </span>
            <ChevronDown className="flex-shrink-0" size={16} />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent collisionPadding={10} align="end" className="w-38">
          <DropdownMenuGroup>
            {Object.values(Template).map((template) => (
              <DropdownMenuItem
                key={template}
                onClick={() => setTemplate(template)}
              >
                <span className="text-smalldoge-3">
                  {templateData[template]}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  const selectedTemplate = useMemo(() => {
    return (
      <EuropassTemplate
        avatar={base64Avatar}
        sections={sections.sort((a, b) => a.order - b.order)}
      />
    );
  }, [base64Avatar, sections]);

  return (
    <div className="flex flex-col h-full">
      <div className="h-10 flex justify-end items-center space-x-4 border-b border-msGray-5 px-4 py-2">
        <div className="mr-auto">{templateDropdown}</div>
        {cvStatusDropdown}
        <ButtonSecondary onClick={onEdit}>Edit</ButtonSecondary>
        <PDFDownloadLink document={selectedTemplate}>
          <ButtonSecondary>Export</ButtonSecondary>
        </PDFDownloadLink>
      </div>
      <div className="p-5 h-full w-full max-w-2xl">
        {/* <AiChat /> */}
        <PDFViewer
          showToolbar={false}
          style={{ width: '100%', height: '100%' }}
        >
          {selectedTemplate}
        </PDFViewer>
      </div>
    </div>
  );
}
