import { cloneDeep } from 'lodash';
import { EducationRecord } from 'shared/types';

import { ButtonSecondary } from '../../../common';
import { EducationItem } from './components/EducationItem/EducationItem';

export const emptyEducationItem: EducationRecord = {
  schoolName: '',
  degree: '',
  description: '',
  startDate: undefined,
  endDate: undefined,
};

interface MemberEducationFormProps {
  data: EducationRecord[];
  readonly?: boolean;
  onDataChange: (data: EducationRecord[]) => void;
}

export const MemberEducationForm = ({
  data,
  readonly,
  onDataChange,
}: MemberEducationFormProps) => {
  function updateHistoryRecord(
    index: number,
    updatedEducationRecord: EducationRecord,
  ) {
    const tempData = cloneDeep(data);
    tempData[index] = updatedEducationRecord;

    onDataChange(tempData);
  }

  function handleCreateRecord() {
    onDataChange(data.concat(emptyEducationItem));
  }

  if (!data.length) {
    return (
      <div className="flex flex-col space-y-2 items-center justify-center border border-dashed border-s-msGray-4 rounded-[8px] px-2 py-6">
        <img src="/images/notes.svg" alt="notes" />
        <span className="text-smalldoge-3">
          There are no education records yet
        </span>
        <ButtonSecondary disabled={readonly} onClick={handleCreateRecord}>
          Add new record
        </ButtonSecondary>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col space-y-5 p-px">
        {data.map((educationRecord, i) => (
          <EducationItem
            key={i}
            data={educationRecord}
            dropDownItems={[
              {
                title: 'Remove',
                onClick: () => {
                  const clonedData = cloneDeep(data);
                  clonedData.splice(i, 1);

                  onDataChange(clonedData);
                },
              },
            ]}
            readonly={readonly}
            onChange={(val) => updateHistoryRecord(i, val)}
          />
        ))}
      </div>
      <ButtonSecondary
        disabled={readonly}
        className="mt-4"
        onClick={handleCreateRecord}
      >
        Add new record
      </ButtonSecondary>
    </div>
  );
};
