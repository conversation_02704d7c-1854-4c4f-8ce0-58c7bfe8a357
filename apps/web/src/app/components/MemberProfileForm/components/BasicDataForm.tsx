import { useMemo } from 'react';
import {
  Currency,
  currencyData,
  MemberSource,
  TimeRange,
  timeRangeData,
  UserType,
  userTypeData,
} from 'shared/types';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ImageInput,
  Input,
  InputAutoWidth,
  TagsInput,
} from '../../common';
import { RowWrapper } from '../components';
import { FormAction, FormState } from '../MemberProfileForm';

import { cn } from '@/lib/utils';

const orgLevels = [
  { id: 'l1', name: 'Level 1', color: '#278f24' },
  { id: 'l2', name: 'Level 2', color: '#4ECDC4' },
  { id: 'l3', name: 'Level 3', color: '#45B7D1' },
  { id: 'l4', name: 'Level 4', color: '#FFBE0B' },
  { id: 'l5', name: 'Level 5', color: '#FB5607' },
  { id: 'l6', name: 'Level 6', color: '#8338EC' },
  { id: 'l7', name: 'Level 7', color: '#3A86FF' },
  { id: 'l8', name: 'Level 8', color: '#06D6A0' },
  { id: 'l9', name: 'Level 9', color: '#FF006E' },
];

interface BasicDataFormProps {
  memberSource?: MemberSource;
  profileData: FormState;
  onUpdate: React.Dispatch<FormAction>;
}

export function BasicDataForm({
  memberSource,
  profileData,
  onUpdate,
}: BasicDataFormProps) {
  const selectedLevel = useMemo(
    () => orgLevels.find((l) => l.id === profileData.currentLevel),
    [profileData.currentLevel, orgLevels],
  );

  return (
    <div className="flex flex-col space-y-2 p-4 border border-msGray-5 rounded-[8px] mb-4">
      <RowWrapper label="Profile Photo">
        <ImageInput
          url={profileData.avatarPreview}
          disabled={memberSource === MemberSource.muchskills}
          onImageUpdate={(file, previewUrl) => {
            onUpdate({ type: 'avatarFile', value: file });
            onUpdate({ type: 'avatarPreview', value: previewUrl });
          }}
        />
      </RowWrapper>

      <RowWrapper label="First name" required={true}>
        <Input
          value={profileData.firstName}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          onChange={(e) =>
            onUpdate({ type: 'firstName', value: e.target.value })
          }
        />
      </RowWrapper>

      <RowWrapper label="Last name">
        <Input
          value={profileData.lastName}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          onChange={(e) =>
            onUpdate({ type: 'lastName', value: e.target.value })
          }
        />
      </RowWrapper>

      <RowWrapper label="Email">
        <Input
          value={profileData.email}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          onChange={(e) => onUpdate({ type: 'email', value: e.target.value })}
        />
      </RowWrapper>

      <RowWrapper label="Location">
        <Input
          value={profileData.location}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          onChange={(e) =>
            onUpdate({ type: 'location', value: e.target.value })
          }
        />
      </RowWrapper>

      <RowWrapper label="Telephone">
        <Input
          value={profileData.telephone}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          onChange={(e) =>
            onUpdate({ type: 'telephone', value: e.target.value })
          }
        />
      </RowWrapper>

      <RowWrapper label="Current position">
        <Input
          value={profileData.currentPosition}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          disabled={memberSource === MemberSource.muchskills}
          onChange={(e) =>
            onUpdate({ type: 'currentPosition', value: e.target.value })
          }
        />
      </RowWrapper>

      {/* <RowWrapper label="Current level">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center h-9">
              <div className="flex items-center space-x-1 cursor-pointer">
                <span
                  className="w-4 h-4 rounded-full"
                  style={{
                    backgroundColor: selectedLevel?.color || '#AFAFAF',
                  }}
                />
                <span className="font-bold text-smalldoge-3 text-msGray-2">
                  {selectedLevel ? selectedLevel.name : 'Select level'}
                </span>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="start"
            className="overflow-auto prevent-drawer-outside-click max-h-48"
          >
            <DropdownMenuGroup>
              {orgLevels.map((level) => (
                <DropdownMenuItem
                  key={level.id}
                  onClick={() =>
                    onUpdate({ type: 'currentLevel', value: level.id })
                  }
                >
                  <div className="flex items-center space-x-1">
                    <span
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: level.color }}
                    />
                    <span className="font-bold text-smalldoge-3">
                      {level.name}
                    </span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </RowWrapper> */}

      <RowWrapper label="Type">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center h-9">
              <div
                className={cn(
                  'w-fit max-w-full bg-msGray-6 rounded-[100px] px-2 cursor-pointer',
                  profileData.type && userTypeData[profileData.type].color,
                )}
              >
                <span className="font-bold truncate text-smalldoge-3">
                  {profileData.type
                    ? userTypeData[profileData.type].name
                    : 'Select type'}
                </span>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="start"
            className="overflow-auto prevent-drawer-outside-click max-h-48"
          >
            <DropdownMenuGroup>
              {Object.values(UserType).map((type) => (
                <DropdownMenuItem
                  key={type}
                  onClick={() => onUpdate({ type: 'type', value: type })}
                >
                  <div
                    className={cn(
                      'w-fit max-w-full bg-msGray-6 rounded-[100px] px-2',
                      userTypeData[type].color,
                    )}
                  >
                    <span className="font-bold truncate text-smalldoge-3">
                      {userTypeData[type].name}
                    </span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </RowWrapper>

      {/* <RowWrapper label="Associate clients">
        <div className="flex items-center flex-grow w-full space-x-1 h-9">
          {profileData.clients.map((client) => (
            <div
              key={client}
              className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2"
            >
              <span className="font-bold truncate text-smalldoge-3">
                {client}
              </span>
            </div>
          ))}
        </div>
      </RowWrapper> */}

      <RowWrapper label="Default rate">
        <div className="flex items-center flex-grow w-full h-9">
          <div className="flex items-center space-x-2">
            <div className="relative rounded-sm bg-msGray-6">
              <span className="pl-4.5 pr-2 font-bold opacity-0 text-smalldoge-3">
                {profileData.costRate.amount}
              </span>
              <div className="absolute top-0 left-0">
                <Input
                  type="number"
                  prefixElement={
                    <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                      {currencyData[profileData.costRate.currency].sign}
                    </span>
                  }
                  className="w-full h-6 pl-4 pr-2 font-bold border-0 shadow-none outline-none text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={profileData.costRate.amount.toString()}
                  onChange={(e) =>
                    onUpdate({
                      type: 'costRate',
                      value: {
                        ...profileData.costRate,
                        amount: +e.target.value,
                      },
                    })
                  }
                />
              </div>
            </div>
            <span>/</span>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {timeRangeData[
                      profileData.costRate.timeRange
                    ].name.toUpperCase()}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.values(TimeRange).map((range) => (
                    <DropdownMenuItem
                      key={range}
                      onClick={() =>
                        onUpdate({
                          type: 'costRate',
                          value: {
                            ...profileData.costRate,
                            timeRange: range,
                          },
                        })
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {timeRangeData[range].name.toUpperCase()}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center ml-auto space-x-2">
            {/* <span
              className="font-bold cursor-pointer text-smalldoge-4 text-msBlue-1"
              onClick={() => {
                //TODO: Find out what exactly this button should do
              }}
            >
              Reset to default
            </span> */}
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {currencyData[profileData.costRate.currency].name}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.values(Currency).map((curr) => (
                    <DropdownMenuItem
                      key={curr}
                      onClick={() =>
                        onUpdate({
                          type: 'costRate',
                          value: {
                            ...profileData.costRate,
                            currency: curr,
                          },
                        })
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {currencyData[curr].name}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </RowWrapper>

      <RowWrapper label="Avg. cost to company">
        <div className="flex items-center flex-grow w-full h-9">
          <div className="flex items-center space-x-2">
            <div className="relative rounded-sm bg-msGray-6">
              <span className="pl-4.5 pr-2 font-bold opacity-0 text-smalldoge-3">
                {profileData.costToCompany.amount}
              </span>
              <div className="absolute top-0 left-0">
                <Input
                  prefixElement={
                    <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                      {currencyData[profileData.costToCompany.currency].sign}
                    </span>
                  }
                  className="w-full h-6 pl-4 pr-2 font-bold border-0 shadow-none outline-none text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={profileData.costToCompany.amount.toString()}
                  onChange={(e) =>
                    onUpdate({
                      type: 'costToCompany',
                      value: {
                        ...profileData.costToCompany,
                        amount: +e.target.value,
                      },
                    })
                  }
                />
              </div>
            </div>
            <span>/</span>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {timeRangeData[
                      profileData.costToCompany.timeRange
                    ].name.toUpperCase()}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.values(TimeRange).map((range) => (
                    <DropdownMenuItem
                      key={range}
                      onClick={() =>
                        onUpdate({
                          type: 'costToCompany',
                          value: {
                            ...profileData.costToCompany,
                            timeRange: range,
                          },
                        })
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {timeRangeData[range].name.toUpperCase()}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-6 ml-auto rounded-sm cursor-pointer bg-msGray-6">
                <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                  {currencyData[profileData.costToCompany.currency].name}
                </span>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {Object.values(Currency).map((curr) => (
                  <DropdownMenuItem
                    key={curr}
                    onClick={() =>
                      onUpdate({
                        type: 'costToCompany',
                        value: {
                          ...profileData.costToCompany,
                          currency: curr,
                        },
                      })
                    }
                  >
                    <span className="font-bold truncate text-smalldoge-3">
                      {currencyData[curr].name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </RowWrapper>

      {/* <RowWrapper label="Muchskills profile">
        <Input
          value={muchskillsLink}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          onChange={(e) => setMuchskillsLink(e.target.value)}
        />
      </RowWrapper>

      <RowWrapper label="LinkedIn profile">
        <Input
          value={linkedinLink}
          className="text-smalldoge-3"
          wrapperClassName="flex-grow w-full"
          onChange={(e) => setLinkedinLink(e.target.value)}
        />
      </RowWrapper> */}

      <RowWrapper label="Years of experience">
        <div className="flex items-center space-x-2 h-9">
          <InputAutoWidth
            value={profileData.yearsOfExp}
            onChange={(value) => onUpdate({ type: 'yearsOfExp', value })}
          />
          <span className="font-bold text-smalldoge-3 text-msGray-2">
            years
          </span>
        </div>
      </RowWrapper>

      <RowWrapper label="Languages">
        <TagsInput
          className="flex-grow w-full text-smalldoge-3"
          value={profileData.languages}
          onValueChange={(value) => onUpdate({ type: 'languages', value })}
        />
      </RowWrapper>
    </div>
  );
}
