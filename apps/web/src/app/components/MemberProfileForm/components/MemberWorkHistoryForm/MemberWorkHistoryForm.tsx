import { cloneDeep } from 'lodash';
import { WorkHistoryRecord } from 'shared/types';

import { WorkHistoryItem } from './components';
import { ButtonSecondary } from '../../../common';

export const emptyWorkHistoryRecord: WorkHistoryRecord = {
  companyName: '',
  roleTitle: '',
  description: '',
  startDate: new Date(),
  endDate: undefined,
  isCurrent: false,
};

interface MemberWorkHistoryFormProps {
  data: WorkHistoryRecord[];
  readonly?: boolean;
  onDataChange: (data: WorkHistoryRecord[]) => void;
}

export const MemberWorkHistoryForm = ({
  data,
  readonly,
  onDataChange,
}: MemberWorkHistoryFormProps) => {
  function updateHistoryRecord(
    index: number,
    updatedHistoryRecord: WorkHistoryRecord,
  ) {
    const tempData = cloneDeep(data);
    tempData[index] = updatedHistoryRecord;

    onDataChange(tempData);
  }

  function handleCreateRecord() {
    onDataChange(data.concat(emptyWorkHistoryRecord));
  }

  if (!data.length) {
    return (
      <div className="flex flex-col space-y-2 items-center justify-center border border-dashed border-s-msGray-4 rounded-[8px] px-2 py-6">
        <img src="/images/notes.svg" alt="notes" />
        <span className="text-smalldoge-3">
          There are no work experience records yet
        </span>
        <ButtonSecondary disabled={readonly} onClick={handleCreateRecord}>
          Add new record
        </ButtonSecondary>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col space-y-5 p-px">
        {data.map((historyRecord, i) => (
          <WorkHistoryItem
            key={i}
            data={historyRecord}
            dropDownItems={[
              {
                title: 'Remove',
                onClick: () => {
                  const clonedData = cloneDeep(data);
                  clonedData.splice(i, 1);

                  onDataChange(clonedData);
                },
              },
            ]}
            readonly={readonly}
            onChange={(val) => updateHistoryRecord(i, val)}
          />
        ))}
      </div>
      <ButtonSecondary
        disabled={readonly}
        className="mt-4"
        onClick={handleCreateRecord}
      >
        Add new record
      </ButtonSecondary>
    </div>
  );
};
