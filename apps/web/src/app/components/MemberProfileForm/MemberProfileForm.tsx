import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useReducer } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Certification,
  CostRate,
  Currency,
  EducationRecord,
  Member,
  MemberSource,
  memberSourceData,
  Skill,
  TimeRange,
  UserType,
  WorkHistoryRecord,
} from 'shared/types';
import { toast } from 'sonner';

import {
  BasicDataForm,
  CertificationsForm,
  MemberEducationForm,
  MemberWorkHistoryForm,
  SkillsForm,
} from './components';
import {
  createMemberRequest,
  updateMemberRequest,
} from '../../helpers/requests';

import {
  ButtonSecondary,
  MemberAvatarWithSource,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components';

const defaultCostRateData: CostRate = {
  currency: Currency.usd,
  amount: 0,
  timeRange: TimeRange.month,
};

export type FormState = {
  avatarFile: File | null | undefined;
  avatarPreview: string | null | undefined;
  firstName: string;
  lastName: string;
  email: string;
  location: string;
  telephone: string;
  currentPosition: string;
  currentLevel: string | undefined;
  type: UserType | undefined;
  clients: string[];
  costRate: CostRate;
  costToCompany: CostRate;
  yearsOfExp: number | undefined;
  languages: string[];
  education: EducationRecord[];
  workExperience: WorkHistoryRecord[];
  skills: Skill[];
  certifications: Certification[];
};

export const initialFormState: FormState = {
  avatarFile: undefined,
  avatarPreview: undefined,
  firstName: '',
  lastName: '',
  email: '',
  location: '',
  telephone: '',
  currentPosition: '',
  currentLevel: '',
  type: undefined,
  clients: [],
  costRate: defaultCostRateData,
  costToCompany: defaultCostRateData,
  yearsOfExp: 0,
  languages: [],
  education: [],
  workExperience: [],
  skills: [],
  certifications: [],
};

export type FormAction =
  | {
      [K in keyof FormState]: { type: K; value: FormState[K] };
    }[keyof FormState]
  | { type: 'prefillForm'; value: FormState }
  | { type: 'reset' };

export function formReducer(state: FormState, action: FormAction): FormState {
  if (action.type === 'prefillForm') {
    return action.value;
  }

  if (action.type === 'reset') {
    return initialFormState;
  }

  return {
    ...state,
    [action.type]: action.value,
  };
}

interface MemberProfileFormProps {
  member?: Member;
}

export function MemberProfileForm({ member }: MemberProfileFormProps) {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [formState, formStateDispatch] = useReducer(
    formReducer,
    initialFormState,
  );

  //TODO: Rethink these fields
  // const [muchskillsLink, setMuchskillsLink] = useState<string>('');
  // const [linkedinLink, setLinkedinLink] = useState<string>('');

  useEffect(() => {
    if (member) {
      formStateDispatch({
        type: 'prefillForm',
        value: {
          avatarFile: undefined,
          avatarPreview: member.avatar,
          firstName: member.firstName,
          lastName: member.lastName || '',
          email: member.email || '',
          location: member.location || '',
          telephone: member.telephone || '',
          currentPosition: member.currentPosition || '',
          currentLevel: member.currentLevel,
          type: member.type,
          clients: member.clients,
          costRate: member.costRate || defaultCostRateData,
          costToCompany: member.costToCompany || defaultCostRateData,
          yearsOfExp: member.yearsOfExperience || 0,
          languages: member.languages,
          education: member.education.sort((a, b) => {
            if (!a.startDate && !b.startDate) return 0;
            if (!a.startDate) return 1;
            if (!b.startDate) return -1;
            return (
              new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
            );
          }),
          workExperience: member.workExperience.sort(
            (a, b) =>
              new Date(b.startDate).getTime() - new Date(a.startDate).getTime(),
          ),
          certifications: member.certifications,
          skills: member.skills,
        },
      });
    }
  }, [member]);

  const { mutate: createNewMember } = useMutation({
    mutationFn: () =>
      createMemberRequest(
        {
          ...formState,
          avatarFile: undefined,
          avatarPreview: undefined,
          socials: [],
          source: MemberSource.cvinventory,
        },
        formState.avatarFile,
      ),
    //TODO: Add user friendly form validation
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      queryClient.invalidateQueries({ queryKey: ['organizationFullInfo'] });
      formStateDispatch({
        type: 'reset',
      });
      toast.success('Member created!');
    },
  });

  const { mutate: updateMember } = useMutation({
    mutationFn: (memberId: string) =>
      updateMemberRequest(
        memberId,
        {
          ...formState,
          avatarFile: undefined,
          avatarPreview: undefined,
          socials: [],
        },
        formState.avatarFile,
      ),
    //TODO: Add user friendly form validation
    onError: (error) => {
      toast.error(error.message);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      queryClient.invalidateQueries({ queryKey: ['organizationFullInfo'] });
      toast.success('Member updated!');
    },
  });

  return (
    <>
      {member && (
        <div className="mb-5">
          <div className="flex items-center mb-2">
            <MemberAvatarWithSource member={member} />
            <div className="flex flex-col ml-2">
              <span className="font-bold truncate text-smalldoge-3">
                {`${member.firstName} ${member.lastName}`}
              </span>
              {member.source !== MemberSource.cvinventory && (
                <span className="truncate text-smalldoge-5 text-msGray-2">
                  Skills profile synced to{' '}
                  {memberSourceData[member.source].name}
                </span>
              )}
            </div>
            <ButtonSecondary
              className="w-16 ml-auto"
              onClick={() => navigate(`/member/${member._id}`)}
            >
              CV list
            </ButtonSecondary>
          </div>
          {member.source === MemberSource.muchskills && (
            <span className="italic text-smalldoge-3 text-msRed-1">
              This profile is managed through your HR system. To update basic
              details, please make the changes in your HRIS tool, then resync
              members to pull updated data.
            </span>
          )}
        </div>
      )}
      <Tabs defaultValue="basicProfile">
        <TabsList>
          <TabsTrigger value="basicProfile">
            <span className="uppercase">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="education">
            <span className="uppercase">Education</span>
          </TabsTrigger>
          <TabsTrigger value="workExperience">
            <span className="uppercase">Work Experience</span>
          </TabsTrigger>
          <TabsTrigger value="certifications">
            <span className="uppercase">Certifications</span>
          </TabsTrigger>
          <TabsTrigger value="skills">
            <span className="uppercase">Skills</span>
          </TabsTrigger>
        </TabsList>
        <div className="flex justify-end mt-2">
          {member ? (
            <ButtonSecondary onClick={() => updateMember(member._id)}>
              Save Changes
            </ButtonSecondary>
          ) : (
            <ButtonSecondary onClick={() => createNewMember()}>
              Create Profile
            </ButtonSecondary>
          )}
        </div>
        <TabsContent value="basicProfile">
          <BasicDataForm
            memberSource={member?.source}
            profileData={formState}
            onUpdate={formStateDispatch}
          />
        </TabsContent>
        <TabsContent value="education">
          <MemberEducationForm
            data={formState.education}
            readonly={member?.source === MemberSource.muchskills}
            onDataChange={(newEducation) => {
              formStateDispatch({ type: 'education', value: newEducation });
            }}
          />
        </TabsContent>
        <TabsContent value="workExperience">
          <MemberWorkHistoryForm
            data={formState.workExperience}
            readonly={member?.source === MemberSource.muchskills}
            onDataChange={(newWorkExperience) => {
              formStateDispatch({
                type: 'workExperience',
                value: newWorkExperience,
              });
            }}
          />
        </TabsContent>
        <TabsContent value="certifications">
          <CertificationsForm certifications={formState.certifications} />
        </TabsContent>
        <TabsContent value="skills">
          <SkillsForm skills={formState.skills} />
        </TabsContent>
      </Tabs>
    </>
  );
}
