import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { DateRange } from 'react-day-picker';
import ReactQuill from 'react-quill';
import { CvPreferencesInput, UpdateCvInput } from 'shared/inputs';
import {
  CostRate,
  Currency,
  currencyData,
  Cv,
  TimeRange,
  timeRangeData,
} from 'shared/types';
import { toast } from 'sonner';

import { createCvRequest, updateCvRequest } from '../../helpers/requests';
import {
  ButtonPrimary,
  Checkbox,
  DateRangePicker,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  InputAutoWidth,
} from '../common';
import { RowWrapper } from '../MemberProfileForm/components';

const defaultCostRateData: CostRate = {
  currency: Currency.usd,
  amount: 0,
  timeRange: TimeRange.month,
};

interface CvSettingsFormProps {
  memberId: string;
  initialData?: Cv;
  onCanceled: () => void;
  onSubmitted: () => void;
}

export function CvSettingsForm({
  memberId,
  initialData,
  onCanceled,
  onSubmitted,
}: CvSettingsFormProps) {
  const queryClient = useQueryClient();

  const [title, setTitle] = useState<string>('');
  const [maxPages, setMaxPages] = useState<number>(0);
  // const [role, setRole] = useState<string>('');
  // const [level, setLevel] = useState<string>();
  const [costRate, setCostRate] = useState<CostRate>(defaultCostRateData);
  const [client, setClient] = useState<string>('');
  const [link, setLink] = useState<string>('');
  const [contractRange, setContractRange] = useState<DateRange>();
  const [autoRenewal, setAutoRenewal] = useState<boolean>(false);
  const [leastExperience, setLeastExperience] = useState<number>(0);
  const [maxExperience, setMaxExperience] = useState<number>(0);
  // const [skills] = useState<string[]>(['Figma', 'Blender']);
  const [description, setDescription] = useState<string>('');

  useEffect(() => {
    setTitle(initialData?.preferences.title || '');
    setMaxPages(initialData?.preferences.maxPages || 0);
    // setRole(cvToEdit.preferences.role || '');
    // setLevel(cvToEdit.preferences.level);
    setCostRate(initialData?.preferences.costRate || defaultCostRateData);
    setClient(initialData?.preferences.client || '');
    setLink(initialData?.preferences.link || '');
    setContractRange({
      from: initialData?.preferences.contractStart,
      to: initialData?.preferences.contractEnd,
    });
    setAutoRenewal(initialData?.preferences.autoRenewal || false);
    setLeastExperience(initialData?.preferences.leastExperience || 0);
    setMaxExperience(initialData?.preferences.maxExperience || 0);
    setDescription(initialData?.preferences.description || '');
  }, [initialData]);

  const { mutate: createCv } = useMutation({
    mutationFn: () => {
      const dto: CvPreferencesInput = {
        title,
        maxPages,
        // role,
        // level,
        costRate,
        client,
        link,
        contractStart: contractRange?.from,
        contractEnd: contractRange?.to,
        autoRenewal,
        leastExperience,
        maxExperience,
        // skills,
        description,
      };
      return createCvRequest(memberId, dto);
    },
    onSuccess: () => {
      onSubmitted();
      handleResetFields();
      queryClient.invalidateQueries({
        queryKey: ['memberCvs', { memberId }],
      });
      queryClient.invalidateQueries({ queryKey: ['organizationFullInfo'] });
      toast.message('CV is created!');
    },
    //TODO: Add user friendly form validation
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const { mutate: updateCv } = useMutation({
    mutationFn: (cvId: string) => {
      const dto: UpdateCvInput = {
        preferences: {
          title,
          maxPages,
          // role,
          // level,
          costRate,
          client,
          link,
          contractStart: contractRange?.from,
          contractEnd: contractRange?.to,
          autoRenewal,
          leastExperience,
          maxExperience,
          // skills,
          description,
        },
      };

      return updateCvRequest(cvId, dto);
    },
    onSuccess: () => {
      onSubmitted();
      handleResetFields();
      queryClient.invalidateQueries({ queryKey: ['memberCvs', { memberId }] });
      toast.message('CV is updated!');
    },
    //TODO: Add user friendly form validation
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // const selectedLevel = orgLevels.find((l) => l.id === level);
  // const selectedRole = orgRoles.find((r) => r.id === role);
  // const selectedClient = orgClients.find((c) => c.id === client);

  const handleResetFields = () => {
    setTitle('');
    setMaxPages(0);
    // setRole('');
    // setLevel(undefined);
    setCostRate(defaultCostRateData);
    setClient('');
    setLink('');
    setContractRange(undefined);
    setAutoRenewal(false);
    setLeastExperience(0);
    setMaxExperience(0);
    setDescription('');
  };

  return (
    <div className="flex flex-col space-y-5">
      <span className="font-bold text-smalldoge-1">
        Let’s set some basic requirements for this CV
      </span>
      <div className="flex flex-col p-4 space-y-2">
        <RowWrapper label="Cv Alias" required={true}>
          <Input
            value={title}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setTitle(e.target.value)}
          />
        </RowWrapper>

        <RowWrapper label="Max Pages">
          <InputAutoWidth
            value={maxPages}
            onChange={(value) => setMaxPages(value)}
          />
        </RowWrapper>

        {/* <RowWrapper label="Connected Role">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2">
                  <span className="font-bold truncate text-smalldoge-3">
                    {selectedRole ? selectedRole.name : 'Select role'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgRoles.map((role) => (
                  <DropdownMenuItem
                    key={role.id}
                    onClick={() => setRole(role.id)}
                  >
                    <span className="font-bold text-smalldoge-3">
                      {role.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </RowWrapper> */}

        {/* <RowWrapper label="Level Required">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="flex items-center space-x-1 cursor-pointer">
                  <span
                    className="w-4 h-4 rounded-full"
                    style={{
                      backgroundColor: selectedLevel?.color || '#AFAFAF',
                    }}
                  />
                  <span className="font-bold text-smalldoge-3 text-msGray-2">
                    {selectedLevel ? selectedLevel.name : 'Select level'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgLevels.map((level) => (
                  <DropdownMenuItem
                    key={level.id}
                    onClick={() => setLevel(level.id)}
                  >
                    <div className="flex items-center space-x-1">
                      <span
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: level.color }}
                      />
                      <span className="font-bold text-smalldoge-3">
                        {level.name}
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </RowWrapper> */}

        <RowWrapper label="Contract Rate">
          <div className="flex items-center flex-grow w-full h-9">
            <div className="flex items-center space-x-2">
              <div className="relative rounded-sm bg-msGray-6">
                <span className="px-3 font-bold opacity-0 text-smalldoge-3 text-msGray-2">
                  {costRate.amount}
                </span>
                <div className="absolute top-0 left-0">
                  <Input
                    prefixElement={
                      <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                        {currencyData[costRate.currency].sign}
                      </span>
                    }
                    className="w-full h-6 pl-4 pr-2 font-bold border-0 shadow-none outline-none text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                    value={costRate.amount}
                    onChange={(e) =>
                      setCostRate((val) => ({
                        ...val,
                        amount: +e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
              <span>/</span>
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                    <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                      {timeRangeData[costRate.timeRange].name.toUpperCase()}
                    </span>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  collisionPadding={10}
                  align="start"
                  className="overflow-auto prevent-drawer-outside-click max-h-48"
                >
                  <DropdownMenuGroup>
                    {Object.values(TimeRange).map((range) => (
                      <DropdownMenuItem
                        key={range}
                        onClick={() =>
                          setCostRate((val) => ({ ...val, timeRange: range }))
                        }
                      >
                        <span className="font-bold truncate text-smalldoge-3">
                          {timeRangeData[range].name.toUpperCase()}
                        </span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 ml-auto rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {currencyData[costRate.currency].name}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.values(Currency).map((curr) => (
                    <DropdownMenuItem
                      key={curr}
                      onClick={() =>
                        setCostRate((val) => ({ ...val, currency: curr }))
                      }
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {currencyData[curr].name}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </RowWrapper>

        <RowWrapper label="Client">
          <Input
            value={client}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setClient(e.target.value)}
          />
          {/* <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2">
                  <span className="font-bold truncate text-smalldoge-3">
                    {selectedClient ? selectedClient.name : 'Select client'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgClients.map((client) => (
                  <DropdownMenuItem
                    key={client.id}
                    onClick={() => setClient(client.id)}
                  >
                    <span className="font-bold text-smalldoge-3">
                      {client.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu> */}
        </RowWrapper>

        <RowWrapper label="RFQ or Job Ad">
          <Input
            placeholder="URL goes here"
            value={link}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setLink(e.target.value)}
          />
        </RowWrapper>

        <RowWrapper label="Contract Timeline">
          <div className="flex flex-col space-y-2">
            <DateRangePicker
              date={contractRange}
              onDateChange={setContractRange}
            />
            <div className="flex items-center space-x-2">
              <Checkbox
                id="autoRenew"
                checked={autoRenewal}
                onCheckedChange={(val) => setAutoRenewal(val as boolean)}
              />
              <label
                htmlFor="autoRenew"
                className="text-smalldoge-5 text-msGray-2"
              >
                Contract auto-renews
              </label>
            </div>
          </div>
        </RowWrapper>

        <RowWrapper label="Experience Required">
          <div className="flex items-center space-x-2 h-9">
            <div className="relative rounded-sm bg-msGray-6">
              <div className="h-6 px-3 font-bold opacity-0 min-w-10 text-smalldoge-3 text-msGray-2">
                {leastExperience}
              </div>
              <div className="absolute top-0 left-0">
                <Input
                  type="number"
                  className="h-6 font-bold text-center border-0 shadow-none outline-none min-w-10 text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={leastExperience}
                  onChange={(e) => setLeastExperience(+e.target.value)}
                />
              </div>
            </div>
            <span>-</span>
            <div className="relative rounded-sm bg-msGray-6">
              <div className="h-6 px-3 font-bold opacity-0 min-w-10 text-smalldoge-3 text-msGray-2">
                {maxExperience}
              </div>
              <div className="absolute top-0 left-0">
                <Input
                  type="number"
                  className="h-6 font-bold text-center border-0 shadow-none outline-none min-w-10 text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={maxExperience}
                  onChange={(e) => setMaxExperience(+e.target.value)}
                />
              </div>
            </div>
            <span className="font-bold text-smalldoge-3 text-msGray-2">
              years
            </span>
          </div>
        </RowWrapper>

        {/* <RowWrapper label="Skills Required">
          <div className="flex items-center flex-grow w-full space-x-1 h-9">
            {skills.map((skill) => (
              <div
                key={skill}
                className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2"
              >
                <span className="font-bold truncate text-smalldoge-3">
                  {skill}
                </span>
              </div>
            ))}
          </div>
        </RowWrapper> */}

        <RowWrapper label="Description">
          <div className="w-full min-h">
            <ReactQuill
              modules={{
                toolbar: [
                  ['bold', 'italic', 'underline'],
                  [{ list: 'ordered' }, { list: 'bullet' }],
                ],
              }}
              theme="snow"
              value={description}
              onChange={(value, delta, source) => {
                if (source === 'user') {
                  setDescription(value);
                }
              }}
            />
          </div>
        </RowWrapper>
      </div>
      <div className="flex justify-end mt-5 space-x-2">
        <ButtonPrimary variant={'white'} onClick={onCanceled}>
          Cancel
        </ButtonPrimary>
        {initialData ? (
          <ButtonPrimary onClick={() => updateCv(initialData._id)}>
            Save Changes
          </ButtonPrimary>
        ) : (
          <ButtonPrimary onClick={() => createCv()}>Create</ButtonPrimary>
        )}
      </div>
    </div>
  );
}
