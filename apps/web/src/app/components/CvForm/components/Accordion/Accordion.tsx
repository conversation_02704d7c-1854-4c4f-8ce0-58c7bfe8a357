import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import classNames from 'classnames';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import React from 'react';
import { SectionBase } from 'shared/types';

import { ButtonSecondary, Checkbox, Icon, Input } from '../../../../components';

interface AccordionProps {
  section: SectionBase;
  children: React.ReactNode;
  onUpdate?: (val: SectionBase) => void;
}

export const Accordion = React.memo(
  ({ section, children, onUpdate }: AccordionProps) => {
    const [open, setOpen] = useState<boolean>(false);
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: section.id });

    const style = {
      zIndex: isDragging ? 1 : 'auto',
      transform: CSS.Translate.toString(transform),
      transition,
    };

    return (
      <div
        ref={setNodeRef}
        style={style}
        className="w-full border-b border-msGray-5 p-4 pl-7 h-fit relative bg-msWhite group"
      >
        {/* Drag handle */}
        <div
          className="absolute top-[calc(50%-12px)] left-0.5 rounded-md transition-all duration-300 hover:bg-msGray-6 cursor-grab opacity-0 group-hover:opacity-100"
          {...attributes}
          {...listeners}
        >
          <Icon source="move" size={24} fillColor="msGray-3" />
        </div>
        {/* Tab content */}
        <div className="flex items-center justify-between space-x-2">
          <Input
            className="border-0 outline-none shadow-none focus-visible:ring-0 w-full text-bigdoge-7 font-black"
            value={section.title}
            onChange={(e) => onUpdate?.({ ...section, title: e.target.value })}
          />
          {/* {section.mandatory && <span className="text-msRed-1">*</span>} */}
          <div className="flex items-center space-x-1">
            <Checkbox
              checked={section.active}
              onCheckedChange={(val) =>
                onUpdate?.({
                  ...section,
                  active: !!val,
                })
              }
            />
            <ButtonSecondary
              variant={'icon'}
              onClick={() => setOpen((val) => !val)}
            >
              <Plus size={14} />
            </ButtonSecondary>
          </div>
        </div>
        {/* Collapsable content */}
        <div
          className={classNames(
            'grid transition-grid-rows duration-300 ease-in-out',
            open ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]',
          )}
        >
          <div className="overflow-hidden">{children}</div>
        </div>
      </div>
    );
  },
);
