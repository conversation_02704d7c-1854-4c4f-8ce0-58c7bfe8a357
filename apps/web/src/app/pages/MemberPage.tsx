import { useMutation, useQuery } from '@tanstack/react-query';
import { uniqueId } from 'lodash';
import { ChevronLeft } from 'lucide-react';
import { useContext, useEffect, useMemo, useReducer, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  AboutMeData,
  CertificationsData,
  EducationDataItem,
  LanguagesData,
  PersonalInfoData,
  Section,
  SectionData,
  SkillsData,
  WorkHistoryDataItem,
} from 'shared/types';
import { useDebounce } from 'use-debounce';

import {
  ButtonSecondary,
  Drawer,
  CvForm,
  CvSettingsForm,
  CvPreview,
} from '../components';
import { CvList } from '../components';
import {
  getCvsByMemberRequest,
  updateCvSectionsRequest,
} from '../helpers/requests';

import { LayoutContext } from '@/components/Layout/Layout';
import { cn } from '@/lib/utils';

export enum ReducerActionType {
  setInitialSections = 'setInitialSections',
  updateSection = 'updateSection',
  updateField = 'updateField',
  updateOrder = 'updateOrder',
}

export type FormReducerAction = {
  type: ReducerActionType;
  sectionId?: string;
  data?: SectionData;
  initialSections?: Section[];
  title?: string;
  active?: boolean;
  order?: number;
};

function sectionsReducer(sections: Section[], action: FormReducerAction) {
  switch (action.type) {
    case ReducerActionType.setInitialSections:
      return action.initialSections as Section[];

    case ReducerActionType.updateField:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return { ...section, data: action.data } as Section;
        }
        return section;
      });

    case ReducerActionType.updateSection:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return {
            ...section,
            title: action.title,
            active: action.active,
          } as Section;
        }
        return section;
      });

    case ReducerActionType.updateOrder:
      return sections.map((section) => {
        if (section.id === action.sectionId && section.order !== action.order) {
          return { ...section, order: action.order } as Section;
        }
        return section;
      });

    default:
      return sections;
  }
}

export function MemberPage() {
  const { memberId } = useParams();
  const { setHeaderCallback } = useContext(LayoutContext);

  const [cvToPreview, setCvToPreview] = useState<string>();
  const [previewActive, setPreviewActive] = useState<boolean>(false);
  const [cvToEditSettings, setCvToEditSetting] = useState<string>();
  const [settingsActive, setSettingsActive] = useState<boolean>(false);
  const [cvEditMode, setCvEditMode] = useState<boolean>(false);
  const [isSmallScreen, setIsSmallScreen] = useState<boolean>(false);
  const [sections, dispatchAction] = useReducer(sectionsReducer, []);
  const [debouncedSections] = useDebounce(sections, 600);

  const { data: memberCvs, isLoading: cvsLoading } = useQuery({
    enabled: !!memberId,
    queryKey: ['memberCvs', { memberId }],
    queryFn: () => getCvsByMemberRequest(memberId as string),
  });

  const { mutate: updateCvSections } = useMutation({
    mutationFn: (cvId: string) =>
      updateCvSectionsRequest(cvId, {
        personalInfo: {
          ...(sections.find((s) => s.id === 'personalInfo') as Section),
          data: (sections.find((s) => s.id === 'personalInfo') as Section)
            .data as PersonalInfoData,
        },
        aboutMe: {
          ...(sections.find((s) => s.id === 'aboutMe') as Section),
          data: (sections.find((s) => s.id === 'aboutMe') as Section)
            .data as AboutMeData,
        },
        workHistory: {
          ...(sections.find((s) => s.id === 'workHistory') as Section),
          data: (sections.find((s) => s.id === 'workHistory') as Section)
            .data as WorkHistoryDataItem[],
        },
        education: {
          ...(sections.find((s) => s.id === 'education') as Section),
          data: (sections.find((s) => s.id === 'education') as Section)
            .data as EducationDataItem[],
        },
        certifications: {
          ...(sections.find((s) => s.id === 'certifications') as Section),
          data: (sections.find((s) => s.id === 'certifications') as Section)
            .data as CertificationsData,
        },
        skills: {
          ...(sections.find((s) => s.id === 'skills') as Section),
          data: (sections.find((s) => s.id === 'skills') as Section)
            .data as SkillsData,
        },
        languages: {
          ...(sections.find((s) => s.id === 'languages') as Section),
          data: (sections.find((s) => s.id === 'languages') as Section)
            .data as LanguagesData,
        },
        customSections: [],
      }),
  });

  useEffect(() => {
    setHeaderCallback(`${'USER NAME'}\`s CV`);

    return () => setHeaderCallback('');
  }, []);

  useEffect(() => {
    const checkWindowSize = () => setIsSmallScreen(window.innerWidth < 1366);

    checkWindowSize();

    window.addEventListener('resize', checkWindowSize);
    return () => window.removeEventListener('resize', checkWindowSize);
  }, []);

  useEffect(() => {
    const cvToDisplay = memberCvs?.find((cv) => cv._id === cvToPreview);

    if (cvToDisplay) {
      const defaultSections = Object.entries(cvToDisplay.sections)
        .filter(([key, value]) => key !== '_id' && key !== 'customSections')
        .map(([key, value]) => ({
          id: key,
          ...value,
        }));

      const customSections = cvToDisplay.sections.customSections.map((sec) => ({
        id: 'customSection' + uniqueId(),
        ...sec,
      }));

      dispatchAction({
        type: ReducerActionType.setInitialSections,
        initialSections: [...defaultSections, ...customSections],
      });
    }
  }, [memberCvs, cvToPreview]);

  const cvDataToChangeSettings = useMemo(
    () => memberCvs?.find((cv) => cv._id === cvToEditSettings),
    [memberCvs, cvToEditSettings],
  );

  if (!memberId) return null;

  return (
    <>
      <div className="flex h-full">
        <div className="flex flex-col w-full">
          {cvEditMode ? (
            <>
              <div className="flex h-10 px-4 py-2 border-b border-msGray-5">
                <ButtonSecondary
                  padding={'iconLeft'}
                  onClick={() => setCvEditMode(false)}
                >
                  <span className="flex items-center">
                    <ChevronLeft size={16} />
                    <span>Back to List</span>
                  </span>
                </ButtonSecondary>
                <div className="flex space-x-2 ml-auto">
                  {isSmallScreen && (
                    <ButtonSecondary onClick={() => setPreviewActive(true)}>
                      Preview
                    </ButtonSecondary>
                  )}
                  {cvToPreview && (
                    <ButtonSecondary
                      onClick={() => updateCvSections(cvToPreview)}
                    >
                      Save Changes
                    </ButtonSecondary>
                  )}
                </div>
              </div>
              <CvForm
                sortedSections={sections.sort((a, b) => a.order - b.order)}
                onSectionUpdate={dispatchAction}
              />
            </>
          ) : (
            <>
              <div className="flex h-10 px-4 py-2 border-b border-msGray-5">
                <ButtonSecondary
                  className="ml-auto"
                  onClick={() => {
                    setCvToEditSetting(undefined);
                    setSettingsActive(true);
                    setCvToPreview(undefined);
                    setPreviewActive(false);
                  }}
                >
                  New CV
                </ButtonSecondary>
              </div>
              <CvList
                memberCvs={memberCvs}
                previewingCv={cvToPreview}
                memberId={memberId}
                isCvsLoading={cvsLoading}
                onCvEdit={(cvId) => {
                  setCvToEditSetting(cvId);
                  setSettingsActive(true);
                  setCvToPreview(undefined);
                  setPreviewActive(false);
                }}
                onCvPreview={(cvId) => {
                  setCvToPreview(cvId);
                  setPreviewActive(true);
                  setCvToEditSetting(undefined);
                  setSettingsActive(false);
                }}
                onNewCvClick={() => {
                  setCvToEditSetting(undefined);
                  setSettingsActive(true);
                  setCvToPreview(undefined);
                  setPreviewActive(false);
                }}
              />
            </>
          )}
        </div>

        {!isSmallScreen && (
          <div
            className={cn(
              'hidden border-l border-msGray-5 w-[640px] flex-shrink-0',
              (settingsActive || previewActive) && 'block',
            )}
          >
            {previewActive && cvToPreview && (
              <CvPreview
                cvId={cvToPreview}
                memberId={memberId}
                sections={debouncedSections}
                onEdit={() => setCvEditMode(true)}
              />
            )}
            {settingsActive && (
              <div className="p-5">
                <CvSettingsForm
                  memberId={memberId}
                  initialData={cvDataToChangeSettings}
                  onCanceled={() => {
                    setSettingsActive(false);
                    setCvToEditSetting(undefined);
                  }}
                  onSubmitted={() => {
                    setSettingsActive(false);
                    setCvToEditSetting(undefined);
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {isSmallScreen && (
        <>
          <Drawer
            active={previewActive}
            onClose={() => setPreviewActive(false)}
          >
            {cvToPreview && (
              <CvPreview
                cvId={cvToPreview}
                memberId={memberId}
                sections={debouncedSections}
                onEdit={() => setCvEditMode(true)}
              />
            )}
          </Drawer>
          <Drawer
            active={settingsActive}
            onClose={() => setSettingsActive(false)}
          >
            <div className="overflow-auto">
              <CvSettingsForm
                memberId={memberId}
                initialData={cvDataToChangeSettings}
                onCanceled={() => {
                  setSettingsActive(false);
                  setCvToEditSetting(undefined);
                }}
                onSubmitted={() => {
                  setSettingsActive(false);
                  setCvToEditSetting(undefined);
                }}
              />
            </div>
          </Drawer>
        </>
      )}
    </>
  );
}
