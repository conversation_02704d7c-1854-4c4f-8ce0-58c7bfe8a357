import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { CaslAbilityFactory } from '../casl-ability.factory';
import { AppAbility } from '../casl.types';
import {
  CHECK_POLICIES_KEY,
  PolicyHandler,
  RequiredPermission,
} from '../decorators/check-policies.decorator';

@Injectable()
export class PoliciesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers =
      this.reflector.get<PolicyHandler[]>(
        CHECK_POLICIES_KEY,
        context.getHandler(),
      ) || [];

    // If no policies are defined, allow access (let other guards handle authentication)
    if (policyHandlers.length === 0) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    // If no user is present but policies are defined,
    // allow access and let AuthGuard handle authentication
    // The policies will be checked after authentication
    if (!user) {
      return true;
    }

    const ability = this.caslAbilityFactory.createForUser(user);

    return policyHandlers.every((handler) =>
      this.execPolicyHandler(handler, ability),
    );
  }

  private execPolicyHandler(handler: PolicyHandler, ability: AppAbility) {
    if (typeof handler === 'function') {
      return handler(ability);
    }

    // Handle RequiredPermission pattern (legacy)
    if ('action' in handler && 'subject' in handler) {
      return ability.can(handler.action, handler.subject);
    }

    // Handle IPolicyHandler pattern
    return handler.handle(ability);
  }
}
